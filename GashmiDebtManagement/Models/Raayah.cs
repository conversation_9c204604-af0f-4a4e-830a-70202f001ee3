using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GashmiDebtManagement.Models
{
    /// <summary>
    /// نموذج بيانات الرعية
    /// </summary>
    public class Raayah
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        [Display(Name = "الاسم الكامل")]
        public string FullName { get; set; } = string.Empty;

        [Display(Name = "تفعيل خصم الحوالة")]
        public bool EnableDiscount { get; set; } = true;

        [Display(Name = "في كشف الأوزري")]
        public bool InKashfOzri { get; set; } = true;

        [Display(Name = "في خارج الكشف")]
        public bool InKharijKashf { get; set; } = false;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقة مع الديون الأسبوعية
        public virtual ICollection<WeeklyDebts> WeeklyDebts { get; set; } = new List<WeeklyDebts>();

        /// <summary>
        /// حساب إجمالي الديون لفترة معينة
        /// </summary>
        public decimal GetTotalDebtsForPeriod(DateTime fromDate, DateTime toDate)
        {
            return WeeklyDebts
                .Where(w => w.DateFrom >= fromDate && w.DateTo <= toDate)
                .Sum(w => w.SamirAmount + w.MaherAmount + w.RaidAmount + w.HaiderAmount + w.LateAmount);
        }

        /// <summary>
        /// حساب إجمالي المبالغ الواصلة لفترة معينة
        /// </summary>
        public decimal GetTotalReceivedForPeriod(DateTime fromDate, DateTime toDate)
        {
            return WeeklyDebts
                .Where(w => w.DateFrom >= fromDate && w.DateTo <= toDate)
                .Sum(w => w.ReceivedAmount);
        }

        /// <summary>
        /// حساب خصم الحوالة لفترة معينة (3%)
        /// </summary>
        public decimal GetDiscountForPeriod(DateTime fromDate, DateTime toDate)
        {
            if (!EnableDiscount) return 0;

            var totalDebts = GetTotalDebtsForPeriod(fromDate, toDate);
            var totalReceived = GetTotalReceivedForPeriod(fromDate, toDate);
            
            return (totalDebts - totalReceived) * 0.03m;
        }

        /// <summary>
        /// حساب الصافي لفترة معينة
        /// </summary>
        public decimal GetNetAmountForPeriod(DateTime fromDate, DateTime toDate)
        {
            var totalDebts = GetTotalDebtsForPeriod(fromDate, toDate);
            var totalReceived = GetTotalReceivedForPeriod(fromDate, toDate);
            var discount = GetDiscountForPeriod(fromDate, toDate);
            
            return totalDebts - totalReceived - discount;
        }

        public override string ToString()
        {
            return FullName;
        }
    }
}
