using Microsoft.EntityFrameworkCore;

namespace GashmiDebtManagement.Models
{
    /// <summary>
    /// سياق قاعدة البيانات الرئيسي
    /// </summary>
    public class ApplicationDbContext : DbContext
    {
        public DbSet<Raayah> <PERSON><PERSON><PERSON> { get; set; } = null!;
        public DbSet<WeeklyDebts> WeeklyDebts { get; set; } = null!;

        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
                // مسار قاعدة البيانات
                var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GashmiDebtManagement.db");
                optionsBuilder.UseSqlite($"Data Source={dbPath}");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // إعداد جدول الرعية
            modelBuilder.Entity<Raayah>(entity =>
            {
                entity.HasKey(e => e.Id);
                entity.Property(e => e.FullName).IsRequired().HasMaxLength(100);
                entity.Property(e => e.EnableDiscount).HasDefaultValue(true);
                entity.Property(e => e.InKashfOzri).HasDefaultValue(true);
                entity.Property(e => e.InKharijKashf).HasDefaultValue(false);
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");

                // فهرس على الاسم
                entity.HasIndex(e => e.FullName).IsUnique();
            });

            // إعداد جدول الديون الأسبوعية
            modelBuilder.Entity<WeeklyDebts>(entity =>
            {
                entity.HasKey(e => e.Id);
                
                // العلاقة مع الرعوي
                entity.HasOne(e => e.Raayah)
                      .WithMany(r => r.WeeklyDebts)
                      .HasForeignKey(e => e.RaayahId)
                      .OnDelete(DeleteBehavior.Cascade);

                // إعداد الأعمدة العشرية
                entity.Property(e => e.SamirAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.MaherAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.RaidAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.HaiderAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.LateAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                entity.Property(e => e.ReceivedAmount).HasColumnType("decimal(18,2)").HasDefaultValue(0);
                
                entity.Property(e => e.CreatedDate).HasDefaultValueSql("datetime('now')");

                // فهرس مركب لمنع تكرار البيانات لنفس الرعوي في نفس الفترة
                entity.HasIndex(e => new { e.RaayahId, e.DateFrom, e.DateTo }).IsUnique();
            });

            // إدراج بيانات تجريبية
            SeedData(modelBuilder);
        }

        /// <summary>
        /// إدراج بيانات تجريبية
        /// </summary>
        private void SeedData(ModelBuilder modelBuilder)
        {
            // بيانات رعية تجريبية
            modelBuilder.Entity<Raayah>().HasData(
                new Raayah 
                { 
                    Id = 1, 
                    FullName = "أحمد محمد الغشمي", 
                    EnableDiscount = true, 
                    InKashfOzri = true, 
                    InKharijKashf = false,
                    CreatedDate = DateTime.Now
                },
                new Raayah 
                { 
                    Id = 2, 
                    FullName = "محمد علي السالمي", 
                    EnableDiscount = true, 
                    InKashfOzri = true, 
                    InKharijKashf = false,
                    CreatedDate = DateTime.Now
                },
                new Raayah 
                { 
                    Id = 3, 
                    FullName = "علي حسن المقطري", 
                    EnableDiscount = false, 
                    InKashfOzri = false, 
                    InKharijKashf = true,
                    CreatedDate = DateTime.Now
                }
            );
        }

        /// <summary>
        /// التأكد من إنشاء قاعدة البيانات
        /// </summary>
        public void EnsureCreated()
        {
            Database.EnsureCreated();
        }

        /// <summary>
        /// تطبيق الترحيلات
        /// </summary>
        public async Task MigrateAsync()
        {
            await Database.MigrateAsync();
        }
    }
}
