using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace GashmiDebtManagement.Models
{
    /// <summary>
    /// نموذج بيانات الديون الأسبوعية
    /// </summary>
    public class WeeklyDebts
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [Display(Name = "معرف الرعوي")]
        public int RaayahId { get; set; }

        [Required]
        [Display(Name = "تاريخ البداية")]
        public DateTime DateFrom { get; set; }

        [Required]
        [Display(Name = "تاريخ النهاية")]
        public DateTime DateTo { get; set; }

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ سمير")]
        public decimal SamirAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ ماهر")]
        public decimal MaherAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ رايد")]
        public decimal RaidAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "مبلغ حيدر")]
        public decimal HaiderAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ المتأخر")]
        public decimal LateAmount { get; set; } = 0;

        [Column(TypeName = "decimal(18,2)")]
        [Display(Name = "المبلغ الواصل")]
        public decimal ReceivedAmount { get; set; } = 0;

        [Display(Name = "تاريخ الإنشاء")]
        public DateTime CreatedDate { get; set; } = DateTime.Now;

        // العلاقة مع الرعوي
        [ForeignKey("RaayahId")]
        public virtual Raayah Raayah { get; set; } = null!;

        /// <summary>
        /// حساب إجمالي ديون الفروع
        /// </summary>
        [NotMapped]
        public decimal TotalBranchesAmount => SamirAmount + MaherAmount + RaidAmount + HaiderAmount;

        /// <summary>
        /// حساب إجمالي الديون (الفروع + المتأخر)
        /// </summary>
        [NotMapped]
        public decimal TotalDebtsAmount => TotalBranchesAmount + LateAmount;

        /// <summary>
        /// حساب خصم الحوالة (3% من الديون - الواصل)
        /// </summary>
        [NotMapped]
        public decimal DiscountAmount 
        { 
            get 
            {
                if (Raayah?.EnableDiscount != true) return 0;
                return (TotalDebtsAmount - ReceivedAmount) * 0.03m;
            }
        }

        /// <summary>
        /// حساب الصافي (الديون - الواصل - الخصم)
        /// </summary>
        [NotMapped]
        public decimal NetAmount => TotalDebtsAmount - ReceivedAmount - DiscountAmount;

        /// <summary>
        /// التحقق من صحة التواريخ (7 أيام)
        /// </summary>
        public bool IsValidWeekPeriod()
        {
            return (DateTo - DateFrom).Days == 6;
        }

        /// <summary>
        /// الحصول على نص فترة الأسبوع
        /// </summary>
        public string GetWeekPeriodText()
        {
            return $"{DateFrom:dd/MM/yyyy} - {DateTo:dd/MM/yyyy}";
        }

        public override string ToString()
        {
            return $"{Raayah?.FullName} - {GetWeekPeriodText()}";
        }
    }
}
