# دليل المستخدم - نظام إدارة ديون الرعية

## مقدمة

مرحباً بك في نظام إدارة ديون الرعية لشركة الغشمي. هذا الدليل سيساعدك على استخدام جميع وظائف النظام بطريقة فعالة.

## بدء التشغيل

1. افتح التطبيق بتشغيل الأمر: `dotnet run`
2. ستظهر لك القائمة الرئيسية مع الخيارات المتاحة
3. استخدم الأرقام للتنقل بين القوائم

## القائمة الرئيسية

```
═══════════════════════════════════════════════════════════
           نظام إدارة ديون الرعية - شركة الغشمي
═══════════════════════════════════════════════════════════

1. إدارة الرعية
2. إدخال البيانات الأسبوعية
3. التقارير
4. الإحصائيات
0. خروج
```

## 1. إدارة الرعية

### إضافة رعوي جديد
1. اختر "1" من القائمة الرئيسية
2. اختر "2" لإضافة رعوي جديد
3. أدخل البيانات المطلوبة:
   - **الاسم الكامل**: اسم الرعوي (مطلوب)
   - **تفعيل خصم الحوالة**: y للتفعيل، n للتعطيل (افتراضي: y)
   - **كشف الأوزري**: y للإدراج، n لعدم الإدراج (افتراضي: y)
   - **خارج الكشف**: y للإدراج، n لعدم الإدراج (افتراضي: n)

### عرض قائمة الرعية
- اختر "1" لعرض جميع الرعية مع إعداداتهم
- يظهر الجدول: الرقم، الاسم، خصم الحوالة، كشف الأوزري، خارج الكشف

### تعديل بيانات رعوي
1. اختر "3" لتعديل رعوي موجود
2. أدخل رقم الرعوي
3. اتركه فارغاً للاحتفاظ بالقيمة الحالية أو أدخل قيمة جديدة

### حذف رعوي
1. اختر "4" لحذف رعوي
2. أدخل رقم الرعوي
3. اكتب "نعم" للتأكيد
4. **تحذير**: سيتم حذف جميع البيانات المرتبطة بالرعوي

### البحث عن رعوي
- اختر "5" للبحث
- أدخل جزء من الاسم
- ستظهر النتائج المطابقة

## 2. إدخال البيانات الأسبوعية

### إدخال بيانات أسبوع جديد
1. اختر "2" من القائمة الرئيسية
2. اختر "1" لإدخال بيانات جديدة
3. أدخل تاريخ بداية الأسبوع بصيغة: `dd/MM/yyyy`
4. سيتم حساب تاريخ النهاية تلقائياً (بعد 6 أيام)
5. لكل رعوي، أدخل:
   - مبلغ سمير
   - مبلغ ماهر
   - مبلغ رايد
   - مبلغ حيدر
   - المبلغ المتأخر
   - المبلغ الواصل
6. اتركه فارغاً للقيمة 0
7. راجع الملخص واختر "y" للحفظ

### عرض البيانات الأسبوعية
1. اختر "2" لعرض البيانات
2. أدخل تاريخ بداية الأسبوع
3. ستظهر جميع البيانات مع الحسابات التلقائية

### تعديل بيانات أسبوع
1. اختر "3" للتعديل
2. أدخل تاريخ بداية الأسبوع
3. عدل البيانات المطلوبة (اتركه فارغاً للاحتفاظ بالقيمة)
4. احفظ التعديلات

### حذف بيانات أسبوع
1. اختر "4" للحذف
2. أدخل تاريخ بداية الأسبوع
3. اكتب "نعم" للتأكيد

## 3. التقارير

### التقرير الكامل
- يعرض جميع الديون والمبالغ الواصلة
- بدون حساب خصم الحوالة
- مفيد للمراجعة العامة

### التقرير مع خصم الحوالة
- يحسب خصم الحوالة (3%) للرعية المفعلين
- يعرض الصافي النهائي بعد الخصم
- الأكثر استخداماً للحسابات النهائية

### كشف البطاقات
- بطاقة منفصلة لكل رعوي
- تفاصيل كاملة لكل فرع
- مناسب للطباعة والتوزيع

### كشف الأوزري
- يعرض فقط الرعية المحددين في "كشف الأوزري"
- تقرير مخصص حسب التصنيف

### خارج الكشف
- يعرض فقط الرعية المحددين في "خارج الكشف"
- تقرير منفصل للفئة الخاصة

### تقرير الفروع
- إحصائيات مفصلة لكل فرع
- النسب المئوية من الإجمالي
- مفيد لتحليل أداء الفروع

### التقرير الشهري
- تجميع البيانات لشهر كامل
- عدد الأسابيع لكل رعوي
- الإجماليات الشهرية

## 4. الإحصائيات

تعرض معلومات سريعة عن:
- إجمالي عدد الرعية
- عدد المفعلين لخصم الحوالة
- عدد الموجودين في كشف الأوزري
- عدد الموجودين في خارج الكشف
- إحصائيات الشهر الحالي

## نصائح مهمة

### تنسيق التواريخ
- استخدم دائماً صيغة: `dd/MM/yyyy`
- مثال: `01/01/2024` لأول يناير 2024

### إدخال المبالغ
- استخدم الأرقام العشرية (مثال: 1500.50)
- اتركه فارغاً للقيمة 0
- لا تستخدم فواصل الآلاف

### النسخ الاحتياطية
- يتم حفظ البيانات في ملف `GashmiDebtManagement.db`
- انسخ هذا الملف دورياً كنسخة احتياطية

### استكشاف الأخطاء
- إذا ظهر خطأ، اقرأ الرسالة بعناية
- تأكد من صحة التواريخ والأرقام
- تأكد من وجود رعية مسجلة قبل إدخال البيانات

## العمليات الحسابية

### خصم الحوالة
```
خصم الحوالة = (إجمالي الديون - المبلغ الواصل) × 3%
```
- يُطبق فقط على الرعية المفعلين
- يُحسب تلقائياً في التقارير

### الصافي
```
الصافي = إجمالي الديون - المبلغ الواصل - خصم الحوالة
```

### إجمالي الديون
```
إجمالي الديون = سمير + ماهر + رايد + حيدر + المتأخر
```

## سير العمل المقترح

### أسبوعياً
1. أدخل بيانات الأسبوع الجديد
2. راجع البيانات باستخدام "عرض البيانات الأسبوعية"
3. أنشئ "التقرير مع خصم الحوالة" للحسابات النهائية

### شهرياً
1. أنشئ "التقرير الشهري" لمراجعة الأداء
2. راجع "تقرير الفروع" لتحليل الفروع
3. احفظ نسخة احتياطية من قاعدة البيانات

### عند الحاجة
- أضف رعية جدد حسب الحاجة
- عدل إعدادات الرعية (خصم الحوالة، التصنيفات)
- استخدم التقارير المختلفة حسب المتطلبات

## الدعم الفني

في حالة مواجهة مشاكل:
1. تأكد من صحة البيانات المدخلة
2. راجع رسائل الخطأ بعناية
3. تأكد من وجود مساحة كافية على القرص الصلب
4. تواصل مع المطور للدعم الفني

---

**ملاحظة**: هذا النظام مصمم خصيصاً لشركة الغشمي ويتبع متطلباتها الخاصة في إدارة ديون الرعية.
