# نظام إدارة ديون الرعية - شركة الغشمي

## 📋 نظرة عامة

نظام شامل لإدارة ديون الرعية في شركة الغشمي، مطور باستخدام C# و Windows Forms مع دعم كامل للغة العربية والواجهة العربية (RTL).

## المميزات الرئيسية

### 🏢 إدارة الرعية
- إضافة وتعديل وحذف بيانات الرعية
- تفعيل/تعطيل خصم الحوالة لكل رعوي
- تصنيف الرعية (كشف الأوزري، خارج الكشف)
- البحث في قائمة الرعية

### 📊 إدخال البيانات الأسبوعية
- إدخال ديون الأسبوع لجميع الرعية دفعة واحدة
- تحديد فترة الأسبوع (7 أيام)
- إدخال مبالغ الفروع الأربعة + المتأخر + الواصل
- تعديل وحذف البيانات الأسبوعية

### 📈 نظام التقارير الشامل
- **التقرير الكامل**: عرض جميع الديون والمبالغ الواصلة
- **التقرير مع خصم الحوالة**: حساب خصم 3% من (الديون - الواصل)
- **كشف البطاقات**: بطاقة منفصلة لكل رعوي
- **كشف الأوزري**: الرعية المحددين في كشف الأوزري فقط
- **خارج الكشف**: الرعية المحددين خارج الكشف
- **تقرير الفروع**: إحصائيات مفصلة لكل فرع
- **التقرير الشهري**: تجميع البيانات الشهرية

### 🔢 العمليات الحسابية
- حساب إجمالي ديون الفروع
- حساب خصم الحوالة (3%)
- حساب الصافي (الديون - الواصل - الخصم)
- إحصائيات شاملة للفترات المختلفة

## التقنيات المستخدمة

- **اللغة**: C# .NET 6
- **قاعدة البيانات**: SQLite مع Entity Framework Core
- **المعمارية**: Repository Pattern
- **واجهة المستخدم**: Console Application (متوافق مع Linux)
- **المكتبات**:
  - Microsoft.EntityFrameworkCore.Sqlite
  - ConsoleTables (لعرض الجداول)

## هيكل المشروع

```
GashmiDebtManagement/
├── Models/                 # نماذج البيانات
│   ├── Raayah.cs          # نموذج الرعية
│   ├── WeeklyDebts.cs     # نموذج الديون الأسبوعية
│   └── ApplicationDbContext.cs
├── Repositories/          # طبقة الوصول للبيانات
│   ├── IRepository.cs
│   ├── Repository.cs
│   ├── IRaayahRepository.cs
│   ├── RaayahRepository.cs
│   ├── IWeeklyDebtsRepository.cs
│   └── WeeklyDebtsRepository.cs
├── Services/              # الخدمات
│   └── CalculationService.cs
├── Forms/                 # واجهات المستخدم
│   ├── MainForm.cs
│   ├── RaayahManagementForm.cs
│   ├── WeeklyDataEntryForm.cs
│   └── ReportsForm.cs
├── Helpers/               # المساعدات
│   └── ArabicHelper.cs
└── Program.cs             # نقطة البداية
```

## قاعدة البيانات

### جدول Raayah (الرعية)
- `Id`: المعرف الفريد
- `FullName`: الاسم الكامل
- `EnableDiscount`: تفعيل خصم الحوالة
- `InKashfOzri`: في كشف الأوزري
- `InKharijKashf`: في خارج الكشف
- `CreatedDate`: تاريخ الإنشاء

### جدول WeeklyDebts (الديون الأسبوعية)
- `Id`: المعرف الفريد
- `RaayahId`: معرف الرعوي
- `DateFrom`: تاريخ بداية الأسبوع
- `DateTo`: تاريخ نهاية الأسبوع
- `SamirAmount`: مبلغ فرع سمير
- `MaherAmount`: مبلغ فرع ماهر
- `RaidAmount`: مبلغ فرع رايد
- `HaiderAmount`: مبلغ فرع حيدر
- `LateAmount`: المبلغ المتأخر
- `ReceivedAmount`: المبلغ الواصل
- `CreatedDate`: تاريخ الإنشاء

## التشغيل

### المتطلبات
- .NET 6 Runtime أو أحدث
- نظام تشغيل يدعم .NET (Windows, Linux, macOS)

### خطوات التشغيل

1. **استنساخ المشروع**:
```bash
git clone [repository-url]
cd GashmiDebtManagement
```

2. **استعادة المكتبات**:
```bash
dotnet restore
```

3. **بناء المشروع**:
```bash
dotnet build
```

4. **تشغيل التطبيق**:
```bash
dotnet run
```

## دليل الاستخدام

### 1. إدارة الرعية
- اختر "1" من القائمة الرئيسية
- يمكنك إضافة رعوي جديد أو تعديل البيانات الموجودة
- تحديد إعدادات خصم الحوالة والتصنيفات

### 2. إدخال البيانات الأسبوعية
- اختر "2" من القائمة الرئيسية
- حدد تاريخ بداية الأسبوع (سيتم حساب النهاية تلقائياً)
- أدخل مبالغ الفروع لكل رعوي
- احفظ البيانات دفعة واحدة

### 3. التقارير
- اختر "3" من القائمة الرئيسية
- حدد نوع التقرير المطلوب
- أدخل فترة التقرير
- اعرض النتائج أو صدرها

### 4. الإحصائيات
- اختر "4" لعرض الإحصائيات العامة
- معلومات عن عدد الرعية والسجلات
- إحصائيات الشهر الحالي

## العمليات الحسابية

### خصم الحوالة
```
خصم الحوالة = (إجمالي الديون - المبلغ الواصل) × 3%
```

### الصافي
```
الصافي = إجمالي الديون - المبلغ الواصل - خصم الحوالة
```

### إجمالي الديون
```
إجمالي الديون = سمير + ماهر + رايد + حيدر + المتأخر
```

## الدعم والصيانة

- قاعدة البيانات تُحفظ في ملف `GashmiDebtManagement.db`
- يتم إنشاء نسخة احتياطية تلقائياً
- جميع العمليات محمية بـ try-catch للتعامل مع الأخطاء

## 🔒 الأمان والنسخ الاحتياطية

### النسخ الاحتياطية التلقائية
- يتم إنشاء نسخة احتياطية تلقائياً عند بدء التطبيق
- يمكن إنشاء نسخ احتياطية يدوية في أي وقت
- يتم ضغط النسخ الاحتياطية لتوفير المساحة

### استعادة البيانات
1. اذهب إلى **الأدوات** → **النسخ الاحتياطية**
2. اختر النسخة المطلوبة
3. انقر **استعادة النسخة المحددة**
4. أعد تشغيل التطبيق

## 📞 الدعم والمساعدة

### الحصول على المساعدة
- **دليل المستخدم**: متاح من قائمة المساعدة
- **اختبار النظام**: للتأكد من سلامة التطبيق
- **الإحصائيات**: لمراقبة أداء النظام

### الإبلاغ عن المشاكل
إذا واجهت أي مشكلة:
1. تأكد من تحديث .NET إلى أحدث إصدار
2. تحقق من وجود مساحة كافية على القرص
3. جرب إنشاء نسخة احتياطية واستعادتها
4. استخدم اختبار النظام للتحقق من سلامة البيانات

## 🔄 التحديثات المستقبلية

### المميزات المخططة
- [ ] دعم قواعد بيانات خارجية (SQL Server)
- [ ] واجهة ويب للوصول عن بُعد
- [ ] تقارير متقدمة مع الرسوم البيانية
- [ ] إشعارات تلقائية للمواعيد المهمة
- [ ] دعم عدة مستخدمين مع الصلاحيات

## المطور

تم تطوير هذا النظام بواسطة Augment Agent لشركة الغشمي لإدارة ديون الرعية بطريقة فعالة ومنظمة.

## الترخيص

هذا المشروع مخصص لشركة الغشمي ولا يُسمح بإعادة توزيعه أو استخدامه تجارياً دون إذن.

---

**تم تطوير هذا النظام بواسطة Augment Agent**
**جميع الحقوق محفوظة © 2024 شركة الغشمي**
