using Microsoft.EntityFrameworkCore;
using GashmiDebtManagement.Models;

namespace GashmiDebtManagement.Repositories
{
    /// <summary>
    /// تطبيق Repository خاص بالديون الأسبوعية
    /// </summary>
    public class WeeklyDebtsRepository : Repository<WeeklyDebts>, IWeeklyDebtsRepository
    {
        public WeeklyDebtsRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<IEnumerable<WeeklyDebts>> GetByPeriodAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Where(w => w.DateFrom >= fromDate && w.DateTo <= toDate)
                .OrderBy(w => w.DateFrom)
                .ThenBy(w => w.Raayah.FullName)
                .ToListAsync();
        }

        public async Task<IEnumerable<WeeklyDebts>> GetByRaayahAndPeriodAsync(int raayahId, DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Where(w => w.RaayahId == raayahId && w.DateFrom >= fromDate && w.DateTo <= toDate)
                .OrderBy(w => w.DateFrom)
                .ToListAsync();
        }

        public async Task<IEnumerable<WeeklyDebts>> GetByRaayahAsync(int raayahId)
        {
            return await _dbSet
                .Where(w => w.RaayahId == raayahId)
                .OrderByDescending(w => w.DateFrom)
                .ToListAsync();
        }

        public async Task<IEnumerable<WeeklyDebts>> GetWithRaayahAsync()
        {
            return await _dbSet
                .Include(w => w.Raayah)
                .OrderByDescending(w => w.DateFrom)
                .ThenBy(w => w.Raayah.FullName)
                .ToListAsync();
        }

        public async Task<IEnumerable<WeeklyDebts>> GetByPeriodWithRaayahAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(w => w.Raayah)
                .Where(w => w.DateFrom >= fromDate && w.DateTo <= toDate)
                .OrderBy(w => w.Raayah.FullName)
                .ToListAsync();
        }

        public async Task<bool> ExistsForPeriodAsync(int raayahId, DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .AnyAsync(w => w.RaayahId == raayahId && w.DateFrom == fromDate && w.DateTo == toDate);
        }

        public async Task<WeeklyDebts?> GetLatestByRaayahAsync(int raayahId)
        {
            return await _dbSet
                .Where(w => w.RaayahId == raayahId)
                .OrderByDescending(w => w.DateFrom)
                .FirstOrDefaultAsync();
        }

        public async Task DeleteByPeriodAsync(DateTime fromDate, DateTime toDate)
        {
            var debtsToDelete = await _dbSet
                .Where(w => w.DateFrom == fromDate && w.DateTo == toDate)
                .ToListAsync();

            if (debtsToDelete.Any())
            {
                _dbSet.RemoveRange(debtsToDelete);
            }
        }

        public async Task<(decimal TotalDebts, decimal TotalReceived, decimal TotalDiscount, decimal TotalNet)> GetPeriodStatisticsAsync(DateTime fromDate, DateTime toDate)
        {
            var debts = await GetByPeriodWithRaayahAsync(fromDate, toDate);
            
            var totalDebts = debts.Sum(d => d.TotalDebtsAmount);
            var totalReceived = debts.Sum(d => d.ReceivedAmount);
            var totalDiscount = debts.Sum(d => d.DiscountAmount);
            var totalNet = debts.Sum(d => d.NetAmount);

            return (totalDebts, totalReceived, totalDiscount, totalNet);
        }

        public async Task<(decimal Samir, decimal Maher, decimal Raid, decimal Haider, decimal Late)> GetBranchesTotalsAsync(DateTime fromDate, DateTime toDate)
        {
            var debts = await GetByPeriodAsync(fromDate, toDate);
            
            var samir = debts.Sum(d => d.SamirAmount);
            var maher = debts.Sum(d => d.MaherAmount);
            var raid = debts.Sum(d => d.RaidAmount);
            var haider = debts.Sum(d => d.HaiderAmount);
            var late = debts.Sum(d => d.LateAmount);

            return (samir, maher, raid, haider, late);
        }

        public async Task<bool> BulkInsertWeeklyDebtsAsync(DateTime fromDate, DateTime toDate, Dictionary<int, WeeklyDebts> debtsData)
        {
            try
            {
                // حذف البيانات الموجودة لنفس الفترة
                await DeleteByPeriodAsync(fromDate, toDate);

                // إضافة البيانات الجديدة
                var debtsToAdd = debtsData.Values.ToList();
                await _dbSet.AddRangeAsync(debtsToAdd);

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
