using Microsoft.EntityFrameworkCore;
using GashmiDebtManagement.Models;

namespace GashmiDebtManagement.Repositories
{
    /// <summary>
    /// تطبيق Repository خاص بالرعية
    /// </summary>
    public class RaayahRepository : Repository<Raayah>, IRaayahRepository
    {
        public RaayahRepository(ApplicationDbContext context) : base(context)
        {
        }

        public async Task<Raayah?> GetByNameAsync(string fullName)
        {
            return await _dbSet.FirstOrDefaultAsync(r => r.FullName == fullName);
        }

        public async Task<IEnumerable<Raayah>> GetWithWeeklyDebtsAsync()
        {
            return await _dbSet
                .Include(r => r.WeeklyDebts)
                .OrderBy(r => r.FullName)
                .ToListAsync();
        }

        public async Task<Raayah?> GetWithWeeklyDebtsAsync(int id)
        {
            return await _dbSet
                .Include(r => r.WeeklyDebts)
                .FirstOrDefaultAsync(r => r.Id == id);
        }

        public async Task<IEnumerable<Raayah>> GetWithDiscountEnabledAsync()
        {
            return await _dbSet
                .Where(r => r.EnableDiscount)
                .OrderBy(r => r.FullName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Raayah>> GetInKashfOzriAsync()
        {
            return await _dbSet
                .Where(r => r.InKashfOzri)
                .OrderBy(r => r.FullName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Raayah>> GetInKharijKashfAsync()
        {
            return await _dbSet
                .Where(r => r.InKharijKashf)
                .OrderBy(r => r.FullName)
                .ToListAsync();
        }

        public async Task<IEnumerable<Raayah>> GetWithDebtsForPeriodAsync(DateTime fromDate, DateTime toDate)
        {
            return await _dbSet
                .Include(r => r.WeeklyDebts.Where(w => w.DateFrom >= fromDate && w.DateTo <= toDate))
                .Where(r => r.WeeklyDebts.Any(w => w.DateFrom >= fromDate && w.DateTo <= toDate))
                .OrderBy(r => r.FullName)
                .ToListAsync();
        }

        public async Task<bool> IsNameExistsAsync(string fullName, int? excludeId = null)
        {
            var query = _dbSet.Where(r => r.FullName == fullName);
            
            if (excludeId.HasValue)
            {
                query = query.Where(r => r.Id != excludeId.Value);
            }
            
            return await query.AnyAsync();
        }

        public async Task<(int Total, int WithDiscount, int InOzri, int InKharij)> GetStatisticsAsync()
        {
            var total = await _dbSet.CountAsync();
            var withDiscount = await _dbSet.CountAsync(r => r.EnableDiscount);
            var inOzri = await _dbSet.CountAsync(r => r.InKashfOzri);
            var inKharij = await _dbSet.CountAsync(r => r.InKharijKashf);

            return (total, withDiscount, inOzri, inKharij);
        }
    }
}
