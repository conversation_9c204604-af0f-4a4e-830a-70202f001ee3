using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;
using ConsoleTables;

namespace GashmiDebtManagement.Forms
{
    /// <summary>
    /// نافذة التقارير
    /// </summary>
    public class ReportsForm
    {
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;

        public ReportsForm(IRaayahRepository raayahRepository, IWeeklyDebtsRepository weeklyDebtsRepository)
        {
            _raayahRepository = raayahRepository;
            _weeklyDebtsRepository = weeklyDebtsRepository;
        }

        /// <summary>
        /// عرض نافذة التقارير
        /// </summary>
        public async Task ShowAsync()
        {
            while (true)
            {
                Console.Clear();
                Console.WriteLine("═══════════════════════════════════════════════════════════");
                Console.WriteLine("                        التقارير");
                Console.WriteLine("═══════════════════════════════════════════════════════════");
                Console.WriteLine();

                Console.WriteLine("1. التقرير الكامل");
                Console.WriteLine("2. التقرير مع خصم الحوالة");
                Console.WriteLine("3. كشف البطاقات");
                Console.WriteLine("4. كشف الأوزري");
                Console.WriteLine("5. خارج الكشف");
                Console.WriteLine("6. تقرير الفروع");
                Console.WriteLine("7. تقرير شهري");
                Console.WriteLine("0. العودة للقائمة الرئيسية");
                Console.WriteLine();
                Console.Write("اختر نوع التقرير: ");

                var choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        await ShowCompleteReport();
                        break;
                    case "2":
                        await ShowDiscountReport();
                        break;
                    case "3":
                        await ShowCardsReport();
                        break;
                    case "4":
                        await ShowOzriReport();
                        break;
                    case "5":
                        await ShowKharijReport();
                        break;
                    case "6":
                        await ShowBranchesReport();
                        break;
                    case "7":
                        await ShowMonthlyReport();
                        break;
                    case "0":
                        return;
                    default:
                        Console.WriteLine("خيار غير صحيح، يرجى المحاولة مرة أخرى.");
                        break;
                }

                if (choice != "0")
                {
                    Console.WriteLine("\nاضغط أي مفتاح للمتابعة...");
                    Console.ReadKey();
                }
            }
        }

        /// <summary>
        /// التقرير الكامل
        /// </summary>
        private async Task ShowCompleteReport()
        {
            var (fromDate, toDate) = GetDateRange();
            if (fromDate == DateTime.MinValue) return;

            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                     التقرير الكامل");
            Console.WriteLine($"                 {fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy}");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);

                if (!weeklyData.Any())
                {
                    Console.WriteLine("لا توجد بيانات لهذه الفترة.");
                    return;
                }

                var table = new ConsoleTable("الرعوي", "سمير", "ماهر", "رايد", "حيدر", "متأخر", "إجمالي الديون", "الواصل", "الصافي");

                foreach (var debt in weeklyData.OrderBy(d => d.Raayah.FullName))
                {
                    var netAmount = debt.TotalDebtsAmount - debt.ReceivedAmount;
                    table.AddRow(
                        debt.Raayah.FullName,
                        debt.SamirAmount.ToString("N2"),
                        debt.MaherAmount.ToString("N2"),
                        debt.RaidAmount.ToString("N2"),
                        debt.HaiderAmount.ToString("N2"),
                        debt.LateAmount.ToString("N2"),
                        debt.TotalDebtsAmount.ToString("N2"),
                        debt.ReceivedAmount.ToString("N2"),
                        netAmount.ToString("N2")
                    );
                }

                table.Write();

                // الإجماليات
                var totalDebts = weeklyData.Sum(d => d.TotalDebtsAmount);
                var totalReceived = weeklyData.Sum(d => d.ReceivedAmount);
                var totalNet = totalDebts - totalReceived;

                Console.WriteLine();
                Console.WriteLine("الإجماليات:");
                Console.WriteLine($"إجمالي الديون: {CalculationService.FormatCurrency(totalDebts)}");
                Console.WriteLine($"إجمالي الواصل: {CalculationService.FormatCurrency(totalReceived)}");
                Console.WriteLine($"الصافي: {CalculationService.FormatCurrency(totalNet)}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// التقرير مع خصم الحوالة
        /// </summary>
        private async Task ShowDiscountReport()
        {
            var (fromDate, toDate) = GetDateRange();
            if (fromDate == DateTime.MinValue) return;

            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                التقرير مع خصم الحوالة");
            Console.WriteLine($"                 {fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy}");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);

                if (!weeklyData.Any())
                {
                    Console.WriteLine("لا توجد بيانات لهذه الفترة.");
                    return;
                }

                var table = new ConsoleTable("الرعوي", "إجمالي الديون", "الواصل", "خصم الحوالة", "الصافي", "حالة الخصم");

                foreach (var debt in weeklyData.OrderBy(d => d.Raayah.FullName))
                {
                    var discountStatus = debt.Raayah.EnableDiscount ? "مفعل" : "معطل";
                    table.AddRow(
                        debt.Raayah.FullName,
                        debt.TotalDebtsAmount.ToString("N2"),
                        debt.ReceivedAmount.ToString("N2"),
                        debt.DiscountAmount.ToString("N2"),
                        debt.NetAmount.ToString("N2"),
                        discountStatus
                    );
                }

                table.Write();

                // الإجماليات
                var totals = CalculationService.CalculatePeriodTotals(weeklyData);

                Console.WriteLine();
                Console.WriteLine("الإجماليات:");
                Console.WriteLine($"إجمالي الديون: {CalculationService.FormatCurrency(totals.TotalDebts)}");
                Console.WriteLine($"إجمالي الواصل: {CalculationService.FormatCurrency(totals.TotalReceived)}");
                Console.WriteLine($"إجمالي الخصومات: {CalculationService.FormatCurrency(totals.TotalDiscount)}");
                Console.WriteLine($"الصافي النهائي: {CalculationService.FormatCurrency(totals.TotalNet)}");
                Console.WriteLine($"معدل الخصم: {CalculationService.DISCOUNT_RATE:P}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// كشف البطاقات
        /// </summary>
        private async Task ShowCardsReport()
        {
            var (fromDate, toDate) = GetDateRange();
            if (fromDate == DateTime.MinValue) return;

            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                      كشف البطاقات");
            Console.WriteLine($"                 {fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy}");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);

                if (!weeklyData.Any())
                {
                    Console.WriteLine("لا توجد بيانات لهذه الفترة.");
                    return;
                }

                foreach (var debt in weeklyData.OrderBy(d => d.Raayah.FullName))
                {
                    Console.WriteLine("┌─────────────────────────────────────────────────────────┐");
                    Console.WriteLine($"│ الرعوي: {debt.Raayah.FullName.PadRight(45)} │");
                    Console.WriteLine("├─────────────────────────────────────────────────────────┤");
                    Console.WriteLine($"│ سمير:     {debt.SamirAmount.ToString("N2").PadLeft(10)} ريال                    │");
                    Console.WriteLine($"│ ماهر:     {debt.MaherAmount.ToString("N2").PadLeft(10)} ريال                    │");
                    Console.WriteLine($"│ رايد:     {debt.RaidAmount.ToString("N2").PadLeft(10)} ريال                    │");
                    Console.WriteLine($"│ حيدر:     {debt.HaiderAmount.ToString("N2").PadLeft(10)} ريال                    │");
                    Console.WriteLine($"│ متأخر:    {debt.LateAmount.ToString("N2").PadLeft(10)} ريال                    │");
                    Console.WriteLine("├─────────────────────────────────────────────────────────┤");
                    Console.WriteLine($"│ إجمالي:   {debt.TotalDebtsAmount.ToString("N2").PadLeft(10)} ريال                    │");
                    Console.WriteLine($"│ واصل:     {debt.ReceivedAmount.ToString("N2").PadLeft(10)} ريال                    │");
                    Console.WriteLine($"│ خصم:      {debt.DiscountAmount.ToString("N2").PadLeft(10)} ريال                    │");
                    Console.WriteLine($"│ الصافي:   {debt.NetAmount.ToString("N2").PadLeft(10)} ريال                    │");
                    Console.WriteLine("└─────────────────────────────────────────────────────────┘");
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// كشف الأوزري
        /// </summary>
        private async Task ShowOzriReport()
        {
            var (fromDate, toDate) = GetDateRange();
            if (fromDate == DateTime.MinValue) return;

            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                      كشف الأوزري");
            Console.WriteLine($"                 {fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy}");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                var ozriRaayah = await _raayahRepository.GetInKashfOzriAsync();
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);
                
                var ozriData = weeklyData.Where(d => ozriRaayah.Any(r => r.Id == d.RaayahId));

                if (!ozriData.Any())
                {
                    Console.WriteLine("لا توجد بيانات رعية في كشف الأوزري لهذه الفترة.");
                    return;
                }

                var table = new ConsoleTable("الرعوي", "إجمالي الديون", "الواصل", "خصم الحوالة", "الصافي");

                foreach (var debt in ozriData.OrderBy(d => d.Raayah.FullName))
                {
                    table.AddRow(
                        debt.Raayah.FullName,
                        debt.TotalDebtsAmount.ToString("N2"),
                        debt.ReceivedAmount.ToString("N2"),
                        debt.DiscountAmount.ToString("N2"),
                        debt.NetAmount.ToString("N2")
                    );
                }

                table.Write();

                // الإجماليات
                var totals = CalculationService.CalculatePeriodTotals(ozriData);

                Console.WriteLine();
                Console.WriteLine("إجماليات كشف الأوزري:");
                Console.WriteLine($"إجمالي الديون: {CalculationService.FormatCurrency(totals.TotalDebts)}");
                Console.WriteLine($"إجمالي الواصل: {CalculationService.FormatCurrency(totals.TotalReceived)}");
                Console.WriteLine($"إجمالي الخصومات: {CalculationService.FormatCurrency(totals.TotalDiscount)}");
                Console.WriteLine($"الصافي: {CalculationService.FormatCurrency(totals.TotalNet)}");
                Console.WriteLine($"عدد الرعية: {ozriData.Count()}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// خارج الكشف
        /// </summary>
        private async Task ShowKharijReport()
        {
            var (fromDate, toDate) = GetDateRange();
            if (fromDate == DateTime.MinValue) return;

            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                      خارج الكشف");
            Console.WriteLine($"                 {fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy}");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                var kharijRaayah = await _raayahRepository.GetInKharijKashfAsync();
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);
                
                var kharijData = weeklyData.Where(d => kharijRaayah.Any(r => r.Id == d.RaayahId));

                if (!kharijData.Any())
                {
                    Console.WriteLine("لا توجد بيانات رعية في خارج الكشف لهذه الفترة.");
                    return;
                }

                var table = new ConsoleTable("الرعوي", "إجمالي الديون", "الواصل", "خصم الحوالة", "الصافي");

                foreach (var debt in kharijData.OrderBy(d => d.Raayah.FullName))
                {
                    table.AddRow(
                        debt.Raayah.FullName,
                        debt.TotalDebtsAmount.ToString("N2"),
                        debt.ReceivedAmount.ToString("N2"),
                        debt.DiscountAmount.ToString("N2"),
                        debt.NetAmount.ToString("N2")
                    );
                }

                table.Write();

                // الإجماليات
                var totals = CalculationService.CalculatePeriodTotals(kharijData);

                Console.WriteLine();
                Console.WriteLine("إجماليات خارج الكشف:");
                Console.WriteLine($"إجمالي الديون: {CalculationService.FormatCurrency(totals.TotalDebts)}");
                Console.WriteLine($"إجمالي الواصل: {CalculationService.FormatCurrency(totals.TotalReceived)}");
                Console.WriteLine($"إجمالي الخصومات: {CalculationService.FormatCurrency(totals.TotalDiscount)}");
                Console.WriteLine($"الصافي: {CalculationService.FormatCurrency(totals.TotalNet)}");
                Console.WriteLine($"عدد الرعية: {kharijData.Count()}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// تقرير الفروع
        /// </summary>
        private async Task ShowBranchesReport()
        {
            var (fromDate, toDate) = GetDateRange();
            if (fromDate == DateTime.MinValue) return;

            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                      تقرير الفروع");
            Console.WriteLine($"                 {fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy}");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                var branches = await _weeklyDebtsRepository.GetBranchesTotalsAsync(fromDate, toDate);
                var totalBranches = branches.Samir + branches.Maher + branches.Raid + branches.Haider;
                var grandTotal = totalBranches + branches.Late;

                var table = new ConsoleTable("الفرع", "المبلغ", "النسبة من الفروع", "النسبة من الإجمالي");

                table.AddRow("سمير", 
                    CalculationService.FormatCurrency(branches.Samir),
                    $"{CalculationService.CalculatePercentage(branches.Samir, totalBranches):F2}%",
                    $"{CalculationService.CalculatePercentage(branches.Samir, grandTotal):F2}%");

                table.AddRow("ماهر", 
                    CalculationService.FormatCurrency(branches.Maher),
                    $"{CalculationService.CalculatePercentage(branches.Maher, totalBranches):F2}%",
                    $"{CalculationService.CalculatePercentage(branches.Maher, grandTotal):F2}%");

                table.AddRow("رايد", 
                    CalculationService.FormatCurrency(branches.Raid),
                    $"{CalculationService.CalculatePercentage(branches.Raid, totalBranches):F2}%",
                    $"{CalculationService.CalculatePercentage(branches.Raid, grandTotal):F2}%");

                table.AddRow("حيدر", 
                    CalculationService.FormatCurrency(branches.Haider),
                    $"{CalculationService.CalculatePercentage(branches.Haider, totalBranches):F2}%",
                    $"{CalculationService.CalculatePercentage(branches.Haider, grandTotal):F2}%");

                table.AddRow("المتأخر", 
                    CalculationService.FormatCurrency(branches.Late),
                    "-",
                    $"{CalculationService.CalculatePercentage(branches.Late, grandTotal):F2}%");

                table.Write();

                Console.WriteLine();
                Console.WriteLine("الإجماليات:");
                Console.WriteLine($"إجمالي الفروع: {CalculationService.FormatCurrency(totalBranches)}");
                Console.WriteLine($"المتأخر: {CalculationService.FormatCurrency(branches.Late)}");
                Console.WriteLine($"الإجمالي العام: {CalculationService.FormatCurrency(grandTotal)}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// التقرير الشهري
        /// </summary>
        private async Task ShowMonthlyReport()
        {
            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                     التقرير الشهري");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                Console.Write("أدخل الشهر (MM/yyyy): ");
                if (!DateTime.TryParseExact(Console.ReadLine() + "/01", "MM/yyyy/dd", null, System.Globalization.DateTimeStyles.None, out DateTime monthDate))
                {
                    Console.WriteLine("تاريخ غير صحيح!");
                    return;
                }

                var monthStart = new DateTime(monthDate.Year, monthDate.Month, 1);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                Console.Clear();
                Console.WriteLine("═══════════════════════════════════════════════════════════");
                Console.WriteLine($"                التقرير الشهري - {monthDate:MMMM yyyy}");
                Console.WriteLine("═══════════════════════════════════════════════════════════");
                Console.WriteLine();

                var monthlyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(monthStart, monthEnd);

                if (!monthlyData.Any())
                {
                    Console.WriteLine("لا توجد بيانات لهذا الشهر.");
                    return;
                }

                // تجميع البيانات حسب الرعوي
                var groupedData = monthlyData
                    .GroupBy(d => d.RaayahId)
                    .Select(g => new
                    {
                        RaayahName = g.First().Raayah.FullName,
                        TotalDebts = g.Sum(d => d.TotalDebtsAmount),
                        TotalReceived = g.Sum(d => d.ReceivedAmount),
                        TotalDiscount = g.Sum(d => d.DiscountAmount),
                        TotalNet = g.Sum(d => d.NetAmount),
                        WeeksCount = g.Count()
                    })
                    .OrderBy(x => x.RaayahName);

                var table = new ConsoleTable("الرعوي", "عدد الأسابيع", "إجمالي الديون", "إجمالي الواصل", "إجمالي الخصم", "الصافي");

                foreach (var item in groupedData)
                {
                    table.AddRow(
                        item.RaayahName,
                        item.WeeksCount,
                        item.TotalDebts.ToString("N2"),
                        item.TotalReceived.ToString("N2"),
                        item.TotalDiscount.ToString("N2"),
                        item.TotalNet.ToString("N2")
                    );
                }

                table.Write();

                // الإجماليات الشهرية
                var monthlyTotals = CalculationService.CalculatePeriodTotals(monthlyData);

                Console.WriteLine();
                Console.WriteLine($"إجماليات شهر {monthDate:MMMM yyyy}:");
                Console.WriteLine($"إجمالي الديون: {CalculationService.FormatCurrency(monthlyTotals.TotalDebts)}");
                Console.WriteLine($"إجمالي الواصل: {CalculationService.FormatCurrency(monthlyTotals.TotalReceived)}");
                Console.WriteLine($"إجمالي الخصومات: {CalculationService.FormatCurrency(monthlyTotals.TotalDiscount)}");
                Console.WriteLine($"الصافي: {CalculationService.FormatCurrency(monthlyTotals.TotalNet)}");
                Console.WriteLine($"عدد الأسابيع: {monthlyData.Select(d => d.DateFrom).Distinct().Count()}");
                Console.WriteLine($"عدد الرعية النشطة: {groupedData.Count()}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إنشاء التقرير: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على نطاق التاريخ
        /// </summary>
        private (DateTime fromDate, DateTime toDate) GetDateRange()
        {
            Console.Clear();
            Console.WriteLine("تحديد فترة التقرير:");
            Console.WriteLine();

            Console.Write("أدخل تاريخ البداية (dd/MM/yyyy): ");
            if (!DateTime.TryParseExact(Console.ReadLine(), "dd/MM/yyyy", null, System.Globalization.DateTimeStyles.None, out DateTime fromDate))
            {
                Console.WriteLine("تاريخ غير صحيح!");
                return (DateTime.MinValue, DateTime.MinValue);
            }

            Console.Write("أدخل تاريخ النهاية (dd/MM/yyyy): ");
            if (!DateTime.TryParseExact(Console.ReadLine(), "dd/MM/yyyy", null, System.Globalization.DateTimeStyles.None, out DateTime toDate))
            {
                Console.WriteLine("تاريخ غير صحيح!");
                return (DateTime.MinValue, DateTime.MinValue);
            }

            if (fromDate > toDate)
            {
                Console.WriteLine("تاريخ البداية يجب أن يكون قبل تاريخ النهاية!");
                return (DateTime.MinValue, DateTime.MinValue);
            }

            return (fromDate, toDate);
        }
    }
}
