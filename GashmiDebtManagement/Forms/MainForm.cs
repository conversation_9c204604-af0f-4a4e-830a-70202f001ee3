using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;
using GashmiDebtManagement.Tests;
using ConsoleTables;

namespace GashmiDebtManagement.Forms
{
    /// <summary>
    /// النافذة الرئيسية للتطبيق
    /// </summary>
    public class MainForm
    {
        private readonly ApplicationDbContext _context;
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;

        public MainForm()
        {
            _context = new ApplicationDbContext();
            _raayahRepository = new RaayahRepository(_context);
            _weeklyDebtsRepository = new WeeklyDebtsRepository(_context);
        }

        /// <summary>
        /// تشغيل التطبيق الرئيسي
        /// </summary>
        public async Task RunAsync()
        {
            // التأكد من إنشاء قاعدة البيانات
            _context.EnsureCreated();

            Console.OutputEncoding = System.Text.Encoding.UTF8;
            Console.Clear();

            while (true)
            {
                try
                {
                    ShowMainMenu();
                    var choice = Console.ReadLine();

                    switch (choice)
                    {
                        case "1":
                            await ShowRaayahManagement();
                            break;
                        case "2":
                            await ShowWeeklyDataEntry();
                            break;
                        case "3":
                            await ShowReports();
                            break;
                        case "4":
                            await ShowStatistics();
                            break;
                        case "5":
                            ShowSystemTests();
                            break;
                        case "0":
                            Console.WriteLine("شكراً لاستخدام نظام إدارة ديون الرعية - شركة الغشمي");
                            return;
                        default:
                            Console.WriteLine("خيار غير صحيح، يرجى المحاولة مرة أخرى.");
                            break;
                    }

                    Console.WriteLine("\nاضغط أي مفتاح للمتابعة...");
                    Console.ReadKey();
                    Console.Clear();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"حدث خطأ: {ex.Message}");
                    Console.WriteLine("اضغط أي مفتاح للمتابعة...");
                    Console.ReadKey();
                    Console.Clear();
                }
            }
        }

        /// <summary>
        /// عرض القائمة الرئيسية
        /// </summary>
        private void ShowMainMenu()
        {
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("           نظام إدارة ديون الرعية - شركة الغشمي");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();
            Console.WriteLine("1. إدارة الرعية");
            Console.WriteLine("2. إدخال البيانات الأسبوعية");
            Console.WriteLine("3. التقارير");
            Console.WriteLine("4. الإحصائيات");
            Console.WriteLine("5. اختبار النظام");
            Console.WriteLine("0. خروج");
            Console.WriteLine();
            Console.Write("اختر الوظيفة المطلوبة: ");
        }

        /// <summary>
        /// إدارة الرعية
        /// </summary>
        private async Task ShowRaayahManagement()
        {
            var raayahForm = new RaayahManagementForm(_raayahRepository);
            await raayahForm.ShowAsync();
        }

        /// <summary>
        /// إدخال البيانات الأسبوعية
        /// </summary>
        private async Task ShowWeeklyDataEntry()
        {
            var weeklyForm = new WeeklyDataEntryForm(_raayahRepository, _weeklyDebtsRepository);
            await weeklyForm.ShowAsync();
        }

        /// <summary>
        /// التقارير
        /// </summary>
        private async Task ShowReports()
        {
            var reportsForm = new ReportsForm(_raayahRepository, _weeklyDebtsRepository);
            await reportsForm.ShowAsync();
        }

        /// <summary>
        /// عرض الإحصائيات العامة
        /// </summary>
        private async Task ShowStatistics()
        {
            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                      الإحصائيات العامة");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                var stats = await _raayahRepository.GetStatisticsAsync();
                var totalDebts = await _weeklyDebtsRepository.CountAsync();

                Console.WriteLine($"إجمالي عدد الرعية: {stats.Total}");
                Console.WriteLine($"المفعلين لخصم الحوالة: {stats.WithDiscount}");
                Console.WriteLine($"في كشف الأوزري: {stats.InOzri}");
                Console.WriteLine($"في خارج الكشف: {stats.InKharij}");
                Console.WriteLine($"إجمالي السجلات الأسبوعية: {totalDebts}");

                // إحصائيات الشهر الحالي
                var currentMonth = DateTime.Now;
                var monthStart = new DateTime(currentMonth.Year, currentMonth.Month, 1);
                var monthEnd = monthStart.AddMonths(1).AddDays(-1);

                var monthlyStats = await _weeklyDebtsRepository.GetPeriodStatisticsAsync(monthStart, monthEnd);

                Console.WriteLine();
                Console.WriteLine($"إحصائيات الشهر الحالي ({currentMonth:MMMM yyyy}):");
                Console.WriteLine($"إجمالي الديون: {CalculationService.FormatCurrency(monthlyStats.TotalDebts)}");
                Console.WriteLine($"إجمالي الواصل: {CalculationService.FormatCurrency(monthlyStats.TotalReceived)}");
                Console.WriteLine($"إجمالي الخصومات: {CalculationService.FormatCurrency(monthlyStats.TotalDiscount)}");
                Console.WriteLine($"الصافي: {CalculationService.FormatCurrency(monthlyStats.TotalNet)}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
