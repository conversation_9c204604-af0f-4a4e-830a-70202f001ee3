using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using ConsoleTables;

namespace GashmiDebtManagement.Forms
{
    /// <summary>
    /// نافذة إدارة الرعية
    /// </summary>
    public class RaayahManagementForm
    {
        private readonly IRaayahRepository _raayahRepository;

        public RaayahManagementForm(IRaayahRepository raayahRepository)
        {
            _raayahRepository = raayahRepository;
        }

        /// <summary>
        /// عرض نافذة إدارة الرعية
        /// </summary>
        public async Task ShowAsync()
        {
            while (true)
            {
                Console.Clear();
                Console.WriteLine("═══════════════════════════════════════════════════════════");
                Console.WriteLine("                      إدارة الرعية");
                Console.WriteLine("═══════════════════════════════════════════════════════════");
                Console.WriteLine();

                Console.WriteLine("1. عرض قائمة الرعية");
                Console.WriteLine("2. إضافة رعوي جديد");
                Console.WriteLine("3. تعديل بيانات رعوي");
                Console.WriteLine("4. حذف رعوي");
                Console.WriteLine("5. البحث عن رعوي");
                Console.WriteLine("0. العودة للقائمة الرئيسية");
                Console.WriteLine();
                Console.Write("اختر العملية المطلوبة: ");

                var choice = Console.ReadLine();

                switch (choice)
                {
                    case "1":
                        await ShowRaayahList();
                        break;
                    case "2":
                        await AddNewRaayah();
                        break;
                    case "3":
                        await EditRaayah();
                        break;
                    case "4":
                        await DeleteRaayah();
                        break;
                    case "5":
                        await SearchRaayah();
                        break;
                    case "0":
                        return;
                    default:
                        Console.WriteLine("خيار غير صحيح، يرجى المحاولة مرة أخرى.");
                        break;
                }

                if (choice != "0")
                {
                    Console.WriteLine("\nاضغط أي مفتاح للمتابعة...");
                    Console.ReadKey();
                }
            }
        }

        /// <summary>
        /// عرض قائمة الرعية
        /// </summary>
        private async Task ShowRaayahList()
        {
            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                     قائمة الرعية");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                var raayahList = await _raayahRepository.GetAllAsync();

                if (!raayahList.Any())
                {
                    Console.WriteLine("لا توجد بيانات رعية مسجلة.");
                    return;
                }

                var table = new ConsoleTable("الرقم", "الاسم الكامل", "خصم الحوالة", "كشف الأوزري", "خارج الكشف");

                foreach (var raayah in raayahList.OrderBy(r => r.FullName))
                {
                    table.AddRow(
                        raayah.Id,
                        raayah.FullName,
                        raayah.EnableDiscount ? "نعم" : "لا",
                        raayah.InKashfOzri ? "نعم" : "لا",
                        raayah.InKharijKashf ? "نعم" : "لا"
                    );
                }

                table.Write();
                Console.WriteLine($"\nإجمالي عدد الرعية: {raayahList.Count()}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحميل البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// إضافة رعوي جديد
        /// </summary>
        private async Task AddNewRaayah()
        {
            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                   إضافة رعوي جديد");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                Console.Write("الاسم الكامل: ");
                var fullName = Console.ReadLine()?.Trim();

                if (string.IsNullOrEmpty(fullName))
                {
                    Console.WriteLine("الاسم مطلوب!");
                    return;
                }

                // التحقق من عدم تكرار الاسم
                if (await _raayahRepository.IsNameExistsAsync(fullName))
                {
                    Console.WriteLine("هذا الاسم موجود مسبقاً!");
                    return;
                }

                Console.Write("تفعيل خصم الحوالة؟ (y/n) [y]: ");
                var enableDiscountInput = Console.ReadLine()?.Trim().ToLower();
                var enableDiscount = enableDiscountInput != "n";

                Console.Write("إدراج في كشف الأوزري؟ (y/n) [y]: ");
                var inKashfOzriInput = Console.ReadLine()?.Trim().ToLower();
                var inKashfOzri = inKashfOzriInput != "n";

                Console.Write("إدراج في خارج الكشف؟ (y/n) [n]: ");
                var inKharijKashfInput = Console.ReadLine()?.Trim().ToLower();
                var inKharijKashf = inKharijKashfInput == "y";

                var newRaayah = new Raayah
                {
                    FullName = fullName,
                    EnableDiscount = enableDiscount,
                    InKashfOzri = inKashfOzri,
                    InKharijKashf = inKharijKashf
                };

                await _raayahRepository.AddAsync(newRaayah);
                await _raayahRepository.SaveChangesAsync();

                Console.WriteLine("تم إضافة الرعوي بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في إضافة الرعوي: {ex.Message}");
            }
        }

        /// <summary>
        /// تعديل بيانات رعوي
        /// </summary>
        private async Task EditRaayah()
        {
            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                   تعديل بيانات رعوي");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                Console.Write("أدخل رقم الرعوي: ");
                if (!int.TryParse(Console.ReadLine(), out int id))
                {
                    Console.WriteLine("رقم غير صحيح!");
                    return;
                }

                var raayah = await _raayahRepository.GetByIdAsync(id);
                if (raayah == null)
                {
                    Console.WriteLine("الرعوي غير موجود!");
                    return;
                }

                Console.WriteLine($"البيانات الحالية للرعوي: {raayah.FullName}");
                Console.WriteLine();

                Console.Write($"الاسم الكامل [{raayah.FullName}]: ");
                var newName = Console.ReadLine()?.Trim();
                if (!string.IsNullOrEmpty(newName) && newName != raayah.FullName)
                {
                    if (await _raayahRepository.IsNameExistsAsync(newName, raayah.Id))
                    {
                        Console.WriteLine("هذا الاسم موجود مسبقاً!");
                        return;
                    }
                    raayah.FullName = newName;
                }

                Console.Write($"تفعيل خصم الحوالة؟ (y/n) [{(raayah.EnableDiscount ? "y" : "n")}]: ");
                var enableDiscountInput = Console.ReadLine()?.Trim().ToLower();
                if (!string.IsNullOrEmpty(enableDiscountInput))
                {
                    raayah.EnableDiscount = enableDiscountInput == "y";
                }

                Console.Write($"إدراج في كشف الأوزري؟ (y/n) [{(raayah.InKashfOzri ? "y" : "n")}]: ");
                var inKashfOzriInput = Console.ReadLine()?.Trim().ToLower();
                if (!string.IsNullOrEmpty(inKashfOzriInput))
                {
                    raayah.InKashfOzri = inKashfOzriInput == "y";
                }

                Console.Write($"إدراج في خارج الكشف؟ (y/n) [{(raayah.InKharijKashf ? "y" : "n")}]: ");
                var inKharijKashfInput = Console.ReadLine()?.Trim().ToLower();
                if (!string.IsNullOrEmpty(inKharijKashfInput))
                {
                    raayah.InKharijKashf = inKharijKashfInput == "y";
                }

                await _raayahRepository.UpdateAsync(raayah);
                await _raayahRepository.SaveChangesAsync();

                Console.WriteLine("تم تحديث البيانات بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تحديث البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف رعوي
        /// </summary>
        private async Task DeleteRaayah()
        {
            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                      حذف رعوي");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                Console.Write("أدخل رقم الرعوي: ");
                if (!int.TryParse(Console.ReadLine(), out int id))
                {
                    Console.WriteLine("رقم غير صحيح!");
                    return;
                }

                var raayah = await _raayahRepository.GetWithWeeklyDebtsAsync(id);
                if (raayah == null)
                {
                    Console.WriteLine("الرعوي غير موجود!");
                    return;
                }

                Console.WriteLine($"هل أنت متأكد من حذف الرعوي: {raayah.FullName}؟");
                if (raayah.WeeklyDebts.Any())
                {
                    Console.WriteLine($"تحذير: يوجد {raayah.WeeklyDebts.Count} سجل ديون مرتبط بهذا الرعوي وسيتم حذفها أيضاً!");
                }
                Console.Write("اكتب 'نعم' للتأكيد: ");

                var confirmation = Console.ReadLine()?.Trim();
                if (confirmation != "نعم")
                {
                    Console.WriteLine("تم إلغاء العملية.");
                    return;
                }

                await _raayahRepository.DeleteAsync(raayah);
                await _raayahRepository.SaveChangesAsync();

                Console.WriteLine("تم حذف الرعوي بنجاح!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في حذف الرعوي: {ex.Message}");
            }
        }

        /// <summary>
        /// البحث عن رعوي
        /// </summary>
        private async Task SearchRaayah()
        {
            Console.Clear();
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine("                    البحث عن رعوي");
            Console.WriteLine("═══════════════════════════════════════════════════════════");
            Console.WriteLine();

            try
            {
                Console.Write("أدخل جزء من الاسم للبحث: ");
                var searchTerm = Console.ReadLine()?.Trim();

                if (string.IsNullOrEmpty(searchTerm))
                {
                    Console.WriteLine("يرجى إدخال نص للبحث!");
                    return;
                }

                var results = await _raayahRepository.FindAsync(r => r.FullName.Contains(searchTerm));

                if (!results.Any())
                {
                    Console.WriteLine("لم يتم العثور على نتائج.");
                    return;
                }

                var table = new ConsoleTable("الرقم", "الاسم الكامل", "خصم الحوالة", "كشف الأوزري", "خارج الكشف");

                foreach (var raayah in results.OrderBy(r => r.FullName))
                {
                    table.AddRow(
                        raayah.Id,
                        raayah.FullName,
                        raayah.EnableDiscount ? "نعم" : "لا",
                        raayah.InKashfOzri ? "نعم" : "لا",
                        raayah.InKharijKashf ? "نعم" : "لا"
                    );
                }

                table.Write();
                Console.WriteLine($"\nتم العثور على {results.Count()} نتيجة.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في البحث: {ex.Message}");
            }
        }
    }
}
