@echo off
chcp 65001 > nul
title بناء نظام إدارة ديون الرعية

echo ========================================
echo    بناء نظام إدارة ديون الرعية
echo    شركة الغشمي
echo ========================================
echo.

echo 1. استعادة المكتبات...
dotnet restore
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في استعادة المكتبات!
    pause
    exit /b 1
)

echo.
echo 2. بناء المشروع...
dotnet build --configuration Release
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع!
    pause
    exit /b 1
)

echo.
echo 3. تشغيل الاختبارات...
dotnet test --configuration Release --no-build
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ فشل في بعض الاختبارات!
    echo المتابعة مع التحذير...
)

echo.
echo 4. إنشاء ملف تنفيذي...
dotnet publish --configuration Release --output ./publish --self-contained false
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء الملف التنفيذي!
    pause
    exit /b 1
)

echo.
echo ✅ تم بناء التطبيق بنجاح!
echo 📁 الملفات متاحة في مجلد: publish
echo 🚀 لتشغيل التطبيق: dotnet publish/GashmiDebtManagement.dll
echo.

pause
