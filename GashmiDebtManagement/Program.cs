﻿using GashmiDebtManagement.Forms;

namespace GashmiDebtManagement
{
    class Program
    {
        static async Task Main(string[] args)
        {
            try
            {
                Console.Title = "نظام إدارة ديون الرعية - شركة الغشمي";

                var mainForm = new MainForm();
                await mainForm.RunAsync();

                mainForm.Dispose();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"خطأ في تشغيل التطبيق: {ex.Message}");
                Console.WriteLine("اضغط أي مفتاح للخروج...");
                Console.ReadKey();
            }
        }
    }
}
