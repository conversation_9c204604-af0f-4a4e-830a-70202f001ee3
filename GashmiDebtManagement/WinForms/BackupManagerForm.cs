using System.ComponentModel;
using GashmiDebtManagement.Services;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج إدارة النسخ الاحتياطية
    /// </summary>
    public partial class BackupManagerForm : Form
    {
        // عناصر التحكم
        private Panel? actionPanel;
        private Button? createBackupButton;
        private Button? restoreBackupButton;
        private Button? deleteOldButton;
        private Button? refreshButton;
        private DataGridView? backupsGridView;
        private Label? statusLabel;

        public BackupManagerForm()
        {
            InitializeComponent();
            LoadBackupsList();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "إدارة النسخ الاحتياطية";
            this.Size = new Size(800, 600);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء لوحة الإجراءات
            CreateActionPanel();

            // إنشاء جدول النسخ الاحتياطية
            CreateBackupsGridView();

            // تسمية الحالة
            statusLabel = new Label();
            statusLabel.Text = "جاهز";
            statusLabel.Height = 30;
            statusLabel.Dock = DockStyle.Bottom;
            statusLabel.BackColor = Color.LightGray;
            statusLabel.TextAlign = ContentAlignment.MiddleCenter;
            statusLabel.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.Controls.AddRange(new Control[] {
                actionPanel, backupsGridView, statusLabel
            });

            this.ResumeLayout(false);
        }

        private void CreateActionPanel()
        {
            actionPanel = new Panel();
            actionPanel.Height = 80;
            actionPanel.Dock = DockStyle.Top;
            actionPanel.BackColor = Color.LightBlue;

            // زر إنشاء نسخة احتياطية
            createBackupButton = new Button();
            createBackupButton.Text = "إنشاء نسخة احتياطية";
            createBackupButton.Size = new Size(150, 35);
            createBackupButton.Location = new Point(600, 20);
            createBackupButton.BackColor = Color.LightGreen;
            createBackupButton.Click += CreateBackupButton_Click;

            // زر استعادة نسخة احتياطية
            restoreBackupButton = new Button();
            restoreBackupButton.Text = "استعادة النسخة المحددة";
            restoreBackupButton.Size = new Size(150, 35);
            restoreBackupButton.Location = new Point(440, 20);
            restoreBackupButton.BackColor = Color.LightYellow;
            restoreBackupButton.Enabled = false;
            restoreBackupButton.Click += RestoreBackupButton_Click;

            // زر حذف النسخ القديمة
            deleteOldButton = new Button();
            deleteOldButton.Text = "حذف النسخ القديمة";
            deleteOldButton.Size = new Size(130, 35);
            deleteOldButton.Location = new Point(300, 20);
            deleteOldButton.BackColor = Color.LightCoral;
            deleteOldButton.Click += DeleteOldButton_Click;

            // زر التحديث
            refreshButton = new Button();
            refreshButton.Text = "تحديث";
            refreshButton.Size = new Size(80, 35);
            refreshButton.Location = new Point(210, 20);
            refreshButton.BackColor = Color.LightCyan;
            refreshButton.Click += RefreshButton_Click;

            actionPanel.Controls.AddRange(new Control[] {
                createBackupButton, restoreBackupButton, deleteOldButton, refreshButton
            });
        }

        private void CreateBackupsGridView()
        {
            backupsGridView = new DataGridView();
            backupsGridView.Dock = DockStyle.Fill;
            backupsGridView.AutoGenerateColumns = false;
            backupsGridView.ReadOnly = true;
            backupsGridView.AllowUserToAddRows = false;
            backupsGridView.AllowUserToDeleteRows = false;
            backupsGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            backupsGridView.BackgroundColor = Color.White;
            backupsGridView.Font = new Font("Tahoma", 10F);

            // إعداد الأعمدة
            var fileNameColumn = new DataGridViewTextBoxColumn();
            fileNameColumn.Name = "FileName";
            fileNameColumn.HeaderText = "اسم الملف";
            fileNameColumn.DataPropertyName = "FileName";
            fileNameColumn.Width = 300;

            var createdDateColumn = new DataGridViewTextBoxColumn();
            createdDateColumn.Name = "CreatedDate";
            createdDateColumn.HeaderText = "تاريخ الإنشاء";
            createdDateColumn.DataPropertyName = "FormattedDate";
            createdDateColumn.Width = 150;

            var sizeColumn = new DataGridViewTextBoxColumn();
            sizeColumn.Name = "Size";
            sizeColumn.HeaderText = "الحجم";
            sizeColumn.DataPropertyName = "FormattedSize";
            sizeColumn.Width = 100;

            var pathColumn = new DataGridViewTextBoxColumn();
            pathColumn.Name = "FilePath";
            pathColumn.HeaderText = "المسار";
            pathColumn.DataPropertyName = "FilePath";
            pathColumn.Width = 200;

            backupsGridView.Columns.AddRange(new DataGridViewColumn[] {
                fileNameColumn, createdDateColumn, sizeColumn, pathColumn
            });

            // تنسيق الرؤوس
            backupsGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Navy;
            backupsGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            backupsGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            backupsGridView.ColumnHeadersHeight = 35;

            // تنسيق الصفوف
            backupsGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.AliceBlue;
            backupsGridView.RowsDefaultCellStyle.BackColor = Color.White;
            backupsGridView.RowTemplate.Height = 30;

            // أحداث
            backupsGridView.SelectionChanged += BackupsGridView_SelectionChanged;
            backupsGridView.CellDoubleClick += BackupsGridView_CellDoubleClick;
        }

        private void LoadBackupsList()
        {
            try
            {
                var backups = BackupService.GetAvailableBackups();
                backupsGridView!.DataSource = backups;
                
                statusLabel!.Text = $"تم العثور على {backups.Count} نسخة احتياطية";
                statusLabel.ForeColor = Color.DarkGreen;
            }
            catch (Exception ex)
            {
                statusLabel!.Text = $"خطأ في تحميل النسخ الاحتياطية: {ex.Message}";
                statusLabel.ForeColor = Color.Red;
            }
        }

        private void CreateBackupButton_Click(object? sender, EventArgs e)
        {
            try
            {
                createBackupButton!.Enabled = false;
                createBackupButton.Text = "جاري الإنشاء...";
                statusLabel!.Text = "جاري إنشاء النسخة الاحتياطية...";

                var backupPath = BackupService.CreateBackup();
                
                MessageBox.Show($"تم إنشاء النسخة الاحتياطية بنجاح:\n{backupPath}", 
                    "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                LoadBackupsList();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في إنشاء النسخة الاحتياطية:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                statusLabel!.Text = "فشل في إنشاء النسخة الاحتياطية";
                statusLabel.ForeColor = Color.Red;
            }
            finally
            {
                createBackupButton!.Enabled = true;
                createBackupButton.Text = "إنشاء نسخة احتياطية";
            }
        }

        private void RestoreBackupButton_Click(object? sender, EventArgs e)
        {
            if (GetSelectedBackup() is BackupInfo selectedBackup)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من استعادة النسخة الاحتياطية؟\n" +
                    $"الملف: {selectedBackup.FileName}\n" +
                    $"التاريخ: {selectedBackup.FormattedDate}\n\n" +
                    "تحذير: سيتم استبدال قاعدة البيانات الحالية!",
                    "تأكيد الاستعادة", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        restoreBackupButton!.Enabled = false;
                        restoreBackupButton.Text = "جاري الاستعادة...";
                        statusLabel!.Text = "جاري استعادة النسخة الاحتياطية...";

                        BackupService.RestoreBackup(selectedBackup.FilePath);
                        
                        MessageBox.Show("تم استعادة النسخة الاحتياطية بنجاح!\nيرجى إعادة تشغيل التطبيق.", 
                            "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        statusLabel.Text = "تم استعادة النسخة الاحتياطية بنجاح";
                        statusLabel.ForeColor = Color.DarkGreen;
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"فشل في استعادة النسخة الاحتياطية:\n{ex.Message}", 
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        statusLabel!.Text = "فشل في استعادة النسخة الاحتياطية";
                        statusLabel.ForeColor = Color.Red;
                    }
                    finally
                    {
                        restoreBackupButton!.Enabled = true;
                        restoreBackupButton.Text = "استعادة النسخة المحددة";
                    }
                }
            }
        }

        private void DeleteOldButton_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show(
                "هل تريد حذف النسخ الاحتياطية القديمة؟\n" +
                "سيتم الاحتفاظ بآخر 10 نسخ فقط.",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                    BackupService.CleanOldBackups(10);
                    LoadBackupsList();
                    
                    MessageBox.Show("تم حذف النسخ القديمة بنجاح!", 
                        "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"فشل في حذف النسخ القديمة:\n{ex.Message}", 
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            LoadBackupsList();
        }

        private void BackupsGridView_SelectionChanged(object? sender, EventArgs e)
        {
            restoreBackupButton!.Enabled = backupsGridView?.SelectedRows.Count > 0;
        }

        private void BackupsGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                RestoreBackupButton_Click(sender, e);
            }
        }

        private BackupInfo? GetSelectedBackup()
        {
            if (backupsGridView?.SelectedRows.Count > 0)
            {
                return backupsGridView.SelectedRows[0].DataBoundItem as BackupInfo;
            }
            return null;
        }
    }
}
