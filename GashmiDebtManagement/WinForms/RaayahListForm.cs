using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج عرض قائمة الرعية
    /// </summary>
    public partial class RaayahListForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        private DataGridView? dataGridView;
        private Panel? toolPanel;
        private Button? addButton;
        private Button? editButton;
        private Button? deleteButton;
        private Button? refreshButton;
        private TextBox? searchTextBox;
        private Label? searchLabel;

        public RaayahListForm(IRaayahRepository raayahRepository)
        {
            _raayahRepository = raayahRepository;
            InitializeComponent();
            LoadData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "قائمة الرعية";
            this.Size = new Size(1000, 600);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء لوحة الأدوات
            CreateToolPanel();

            // إنشاء جدول البيانات
            CreateDataGridView();

            this.ResumeLayout(false);
        }

        private void CreateToolPanel()
        {
            toolPanel = new Panel();
            toolPanel.Height = 60;
            toolPanel.Dock = DockStyle.Top;
            toolPanel.BackColor = Color.LightGray;

            // زر إضافة
            addButton = new Button();
            addButton.Text = "إضافة رعوي جديد";
            addButton.Size = new Size(120, 35);
            addButton.Location = new Point(10, 12);
            addButton.BackColor = Color.LightGreen;
            addButton.Click += AddButton_Click;

            // زر تعديل
            editButton = new Button();
            editButton.Text = "تعديل";
            editButton.Size = new Size(80, 35);
            editButton.Location = new Point(140, 12);
            editButton.BackColor = Color.LightBlue;
            editButton.Click += EditButton_Click;

            // زر حذف
            deleteButton = new Button();
            deleteButton.Text = "حذف";
            deleteButton.Size = new Size(80, 35);
            deleteButton.Location = new Point(230, 12);
            deleteButton.BackColor = Color.LightCoral;
            deleteButton.Click += DeleteButton_Click;

            // زر تحديث
            refreshButton = new Button();
            refreshButton.Text = "تحديث";
            refreshButton.Size = new Size(80, 35);
            refreshButton.Location = new Point(320, 12);
            refreshButton.BackColor = Color.LightYellow;
            refreshButton.Click += RefreshButton_Click;

            // مربع البحث
            searchLabel = new Label();
            searchLabel.Text = "البحث:";
            searchLabel.Size = new Size(50, 23);
            searchLabel.Location = new Point(500, 18);
            searchLabel.TextAlign = ContentAlignment.MiddleRight;

            searchTextBox = new TextBox();
            searchTextBox.Size = new Size(200, 23);
            searchTextBox.Location = new Point(560, 15);
            searchTextBox.TextChanged += SearchTextBox_TextChanged;

            toolPanel.Controls.AddRange(new Control[] {
                addButton, editButton, deleteButton, refreshButton,
                searchLabel, searchTextBox
            });

            this.Controls.Add(toolPanel);
        }

        private void CreateDataGridView()
        {
            dataGridView = new DataGridView();
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.AutoGenerateColumns = false;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;
            dataGridView.ReadOnly = true;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.BackgroundColor = Color.White;
            dataGridView.Font = new Font("Tahoma", 10F);

            // إعداد الأعمدة
            var idColumn = new DataGridViewTextBoxColumn();
            idColumn.Name = "Id";
            idColumn.HeaderText = "الرقم";
            idColumn.DataPropertyName = "Id";
            idColumn.Width = 80;

            var nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "FullName";
            nameColumn.HeaderText = "الاسم الكامل";
            nameColumn.DataPropertyName = "FullName";
            nameColumn.Width = 300;

            var discountColumn = new DataGridViewCheckBoxColumn();
            discountColumn.Name = "EnableDiscount";
            discountColumn.HeaderText = "خصم الحوالة";
            discountColumn.DataPropertyName = "EnableDiscount";
            discountColumn.Width = 100;

            var ozriColumn = new DataGridViewCheckBoxColumn();
            ozriColumn.Name = "InKashfOzri";
            ozriColumn.HeaderText = "كشف الأوزري";
            ozriColumn.DataPropertyName = "InKashfOzri";
            ozriColumn.Width = 100;

            var kharijColumn = new DataGridViewCheckBoxColumn();
            kharijColumn.Name = "InKharijKashf";
            kharijColumn.HeaderText = "خارج الكشف";
            kharijColumn.DataPropertyName = "InKharijKashf";
            kharijColumn.Width = 100;

            var createdColumn = new DataGridViewTextBoxColumn();
            createdColumn.Name = "CreatedDate";
            createdColumn.HeaderText = "تاريخ الإنشاء";
            createdColumn.DataPropertyName = "CreatedDate";
            createdColumn.Width = 150;
            createdColumn.DefaultCellStyle.Format = "dd/MM/yyyy";

            dataGridView.Columns.AddRange(new DataGridViewColumn[] {
                idColumn, nameColumn, discountColumn, ozriColumn, kharijColumn, createdColumn
            });

            // تنسيق الرؤوس
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Navy;
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            dataGridView.ColumnHeadersHeight = 35;

            // تنسيق الصفوف
            dataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.LightBlue;
            dataGridView.RowsDefaultCellStyle.BackColor = Color.White;
            dataGridView.RowTemplate.Height = 30;

            // أحداث
            dataGridView.CellDoubleClick += DataGridView_CellDoubleClick;
            dataGridView.SelectionChanged += DataGridView_SelectionChanged;

            this.Controls.Add(dataGridView);
        }

        private async void LoadData()
        {
            try
            {
                var raayahList = await _raayahRepository.GetAllAsync();
                dataGridView!.DataSource = raayahList.OrderBy(r => r.FullName).ToList();
                
                // تحديث عدد السجلات في شريط الحالة
                UpdateStatusBar($"عدد الرعية: {raayahList.Count()}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SearchData(string searchTerm)
        {
            try
            {
                IEnumerable<Raayah> raayahList;
                
                if (string.IsNullOrWhiteSpace(searchTerm))
                {
                    raayahList = await _raayahRepository.GetAllAsync();
                }
                else
                {
                    raayahList = await _raayahRepository.FindAsync(r => r.FullName.Contains(searchTerm));
                }

                dataGridView!.DataSource = raayahList.OrderBy(r => r.FullName).ToList();
                UpdateStatusBar($"عدد النتائج: {raayahList.Count()}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في البحث:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateStatusBar(string message)
        {
            // يمكن تحديث شريط الحالة في النافذة الرئيسية
            if (this.ParentForm is MainForm mainForm)
            {
                // تحديث شريط الحالة
            }
        }

        // معالجات الأحداث
        private void AddButton_Click(object? sender, EventArgs e)
        {
            var addForm = new AddRaayahForm(_raayahRepository);
            addForm.RaayahAdded += (s, args) => LoadData();
            
            if (addForm.ShowDialog() == DialogResult.OK)
            {
                LoadData();
            }
        }

        private void EditButton_Click(object? sender, EventArgs e)
        {
            if (GetSelectedRaayah() is Raayah selectedRaayah)
            {
                var editForm = new EditRaayahForm(_raayahRepository, selectedRaayah);
                editForm.RaayahUpdated += (s, args) => LoadData();
                
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadData();
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار رعوي للتعديل", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private async void DeleteButton_Click(object? sender, EventArgs e)
        {
            if (GetSelectedRaayah() is Raayah selectedRaayah)
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الرعوي: {selectedRaayah.FullName}؟\n" +
                    "سيتم حذف جميع البيانات المرتبطة به أيضاً!",
                    "تأكيد الحذف", 
                    MessageBoxButtons.YesNo, 
                    MessageBoxIcon.Warning);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        await _raayahRepository.DeleteAsync(selectedRaayah);
                        await _raayahRepository.SaveChangesAsync();
                        
                        MessageBox.Show("تم حذف الرعوي بنجاح", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        
                        LoadData();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"فشل في حذف الرعوي:\n{ex.Message}", 
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            else
            {
                MessageBox.Show("يرجى اختيار رعوي للحذف", "تنبيه", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        private void RefreshButton_Click(object? sender, EventArgs e)
        {
            LoadData();
        }

        private void SearchTextBox_TextChanged(object? sender, EventArgs e)
        {
            SearchData(searchTextBox?.Text ?? "");
        }

        private void DataGridView_CellDoubleClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                EditButton_Click(sender, e);
            }
        }

        private void DataGridView_SelectionChanged(object? sender, EventArgs e)
        {
            var hasSelection = dataGridView?.SelectedRows.Count > 0;
            editButton!.Enabled = hasSelection;
            deleteButton!.Enabled = hasSelection;
        }

        private Raayah? GetSelectedRaayah()
        {
            if (dataGridView?.SelectedRows.Count > 0)
            {
                return dataGridView.SelectedRows[0].DataBoundItem as Raayah;
            }
            return null;
        }
    }

    // أحداث مخصصة
    public class RaayahEventArgs : EventArgs
    {
        public Raayah Raayah { get; }
        
        public RaayahEventArgs(Raayah raayah)
        {
            Raayah = raayah;
        }
    }
}
