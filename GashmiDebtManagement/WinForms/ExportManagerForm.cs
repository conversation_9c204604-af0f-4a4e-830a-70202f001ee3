using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج إدارة التصدير
    /// </summary>
    public partial class ExportManagerForm : Form
    {
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;
        private readonly IRaayahRepository _raayahRepository;
        
        // عناصر التحكم
        private Panel? exportPanel;
        private GroupBox? reportTypeGroup;
        private RadioButton? completeReportRadio;
        private RadioButton? discountReportRadio;
        private RadioButton? branchesReportRadio;
        private RadioButton? monthlyReportRadio;
        private RadioButton? raayahListRadio;
        private GroupBox? dateRangeGroup;
        private Label? fromDateLabel;
        private DateTimePicker? fromDatePicker;
        private Label? toDateLabel;
        private DateTimePicker? toDatePicker;
        private Button? exportButton;
        private DataGridView? exportedFilesGrid;
        private Button? openFileButton;
        private Button? deleteFileButton;

        public ExportManagerForm(IWeeklyDebtsRepository weeklyDebtsRepository, IRaayahRepository raayahRepository)
        {
            _weeklyDebtsRepository = weeklyDebtsRepository;
            _raayahRepository = raayahRepository;
            InitializeComponent();
            LoadExportedFiles();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "إدارة التصدير";
            this.Size = new Size(900, 700);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء لوحة التصدير
            CreateExportPanel();

            // إنشاء جدول الملفات المصدرة
            CreateExportedFilesGrid();

            this.Controls.AddRange(new Control[] {
                exportPanel, exportedFilesGrid
            });

            this.ResumeLayout(false);
        }

        private void CreateExportPanel()
        {
            exportPanel = new Panel();
            exportPanel.Height = 300;
            exportPanel.Dock = DockStyle.Top;
            exportPanel.BackColor = Color.LightBlue;

            // مجموعة نوع التقرير
            reportTypeGroup = new GroupBox();
            reportTypeGroup.Text = "نوع التقرير";
            reportTypeGroup.Location = new Point(450, 20);
            reportTypeGroup.Size = new Size(400, 180);

            completeReportRadio = new RadioButton();
            completeReportRadio.Text = "التقرير الكامل";
            completeReportRadio.Location = new Point(250, 30);
            completeReportRadio.Size = new Size(120, 25);
            completeReportRadio.Checked = true;

            discountReportRadio = new RadioButton();
            discountReportRadio.Text = "التقرير مع خصم الحوالة";
            discountReportRadio.Location = new Point(100, 30);
            discountReportRadio.Size = new Size(140, 25);

            branchesReportRadio = new RadioButton();
            branchesReportRadio.Text = "تقرير الفروع";
            branchesReportRadio.Location = new Point(250, 70);
            branchesReportRadio.Size = new Size(120, 25);

            monthlyReportRadio = new RadioButton();
            monthlyReportRadio.Text = "التقرير الشهري";
            monthlyReportRadio.Location = new Point(100, 70);
            monthlyReportRadio.Size = new Size(120, 25);

            raayahListRadio = new RadioButton();
            raayahListRadio.Text = "قائمة الرعية";
            raayahListRadio.Location = new Point(250, 110);
            raayahListRadio.Size = new Size(120, 25);

            reportTypeGroup.Controls.AddRange(new Control[] {
                completeReportRadio, discountReportRadio, branchesReportRadio,
                monthlyReportRadio, raayahListRadio
            });

            // مجموعة نطاق التاريخ
            dateRangeGroup = new GroupBox();
            dateRangeGroup.Text = "نطاق التاريخ";
            dateRangeGroup.Location = new Point(50, 20);
            dateRangeGroup.Size = new Size(380, 120);

            fromDateLabel = new Label();
            fromDateLabel.Text = "من تاريخ:";
            fromDateLabel.Location = new Point(280, 35);
            fromDateLabel.Size = new Size(80, 23);
            fromDateLabel.TextAlign = ContentAlignment.MiddleRight;

            fromDatePicker = new DateTimePicker();
            fromDatePicker.Location = new Point(120, 35);
            fromDatePicker.Size = new Size(150, 23);
            fromDatePicker.Format = DateTimePickerFormat.Short;
            fromDatePicker.Value = DateTime.Now.AddDays(-30);

            toDateLabel = new Label();
            toDateLabel.Text = "إلى تاريخ:";
            toDateLabel.Location = new Point(280, 75);
            toDateLabel.Size = new Size(80, 23);
            toDateLabel.TextAlign = ContentAlignment.MiddleRight;

            toDatePicker = new DateTimePicker();
            toDatePicker.Location = new Point(120, 75);
            toDatePicker.Size = new Size(150, 23);
            toDatePicker.Format = DateTimePickerFormat.Short;
            toDatePicker.Value = DateTime.Now;

            dateRangeGroup.Controls.AddRange(new Control[] {
                fromDateLabel, fromDatePicker, toDateLabel, toDatePicker
            });

            // زر التصدير
            exportButton = new Button();
            exportButton.Text = "تصدير التقرير";
            exportButton.Size = new Size(150, 40);
            exportButton.Location = new Point(375, 220);
            exportButton.BackColor = Color.LightGreen;
            exportButton.Font = new Font("Tahoma", 11F, FontStyle.Bold);
            exportButton.Click += ExportButton_Click;

            exportPanel.Controls.AddRange(new Control[] {
                reportTypeGroup, dateRangeGroup, exportButton
            });

            // أحداث تغيير نوع التقرير
            raayahListRadio.CheckedChanged += (s, e) => {
                dateRangeGroup.Enabled = !raayahListRadio.Checked;
            };
        }

        private void CreateExportedFilesGrid()
        {
            var panel = new Panel();
            panel.Dock = DockStyle.Fill;

            var titleLabel = new Label();
            titleLabel.Text = "الملفات المصدرة";
            titleLabel.Height = 30;
            titleLabel.Dock = DockStyle.Top;
            titleLabel.BackColor = Color.LightGray;
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.Font = new Font("Tahoma", 12F, FontStyle.Bold);

            exportedFilesGrid = new DataGridView();
            exportedFilesGrid.Dock = DockStyle.Fill;
            exportedFilesGrid.AutoGenerateColumns = false;
            exportedFilesGrid.ReadOnly = true;
            exportedFilesGrid.AllowUserToAddRows = false;
            exportedFilesGrid.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            exportedFilesGrid.BackgroundColor = Color.White;

            // إعداد الأعمدة
            var fileNameColumn = new DataGridViewTextBoxColumn();
            fileNameColumn.Name = "FileName";
            fileNameColumn.HeaderText = "اسم الملف";
            fileNameColumn.DataPropertyName = "FileName";
            fileNameColumn.Width = 300;

            var createdDateColumn = new DataGridViewTextBoxColumn();
            createdDateColumn.Name = "CreatedDate";
            createdDateColumn.HeaderText = "تاريخ الإنشاء";
            createdDateColumn.DataPropertyName = "FormattedDate";
            createdDateColumn.Width = 150;

            var sizeColumn = new DataGridViewTextBoxColumn();
            sizeColumn.Name = "Size";
            sizeColumn.HeaderText = "الحجم";
            sizeColumn.DataPropertyName = "FormattedSize";
            sizeColumn.Width = 100;

            exportedFilesGrid.Columns.AddRange(new DataGridViewColumn[] {
                fileNameColumn, createdDateColumn, sizeColumn
            });

            // لوحة الأزرار
            var buttonPanel = new Panel();
            buttonPanel.Height = 60;
            buttonPanel.Dock = DockStyle.Bottom;
            buttonPanel.BackColor = Color.LightGray;

            openFileButton = new Button();
            openFileButton.Text = "فتح الملف";
            openFileButton.Size = new Size(100, 35);
            openFileButton.Location = new Point(650, 12);
            openFileButton.BackColor = Color.LightYellow;
            openFileButton.Enabled = false;
            openFileButton.Click += OpenFileButton_Click;

            deleteFileButton = new Button();
            deleteFileButton.Text = "حذف الملف";
            deleteFileButton.Size = new Size(100, 35);
            deleteFileButton.Location = new Point(540, 12);
            deleteFileButton.BackColor = Color.LightCoral;
            deleteFileButton.Enabled = false;
            deleteFileButton.Click += DeleteFileButton_Click;

            buttonPanel.Controls.AddRange(new Control[] {
                openFileButton, deleteFileButton
            });

            // أحداث
            exportedFilesGrid.SelectionChanged += (s, e) => {
                var hasSelection = exportedFilesGrid.SelectedRows.Count > 0;
                openFileButton.Enabled = hasSelection;
                deleteFileButton.Enabled = hasSelection;
            };

            exportedFilesGrid.CellDoubleClick += (s, e) => {
                if (e.RowIndex >= 0) OpenFileButton_Click(s, e);
            };

            panel.Controls.AddRange(new Control[] {
                titleLabel, exportedFilesGrid, buttonPanel
            });

            this.Controls.Add(panel);
        }

        private async void ExportButton_Click(object? sender, EventArgs e)
        {
            try
            {
                exportButton!.Enabled = false;
                exportButton.Text = "جاري التصدير...";

                string filePath = "";

                if (raayahListRadio!.Checked)
                {
                    // تصدير قائمة الرعية
                    var raayahList = await _raayahRepository.GetAllAsync();
                    filePath = ExportService.ExportRaayahListToCsv(raayahList);
                }
                else
                {
                    var fromDate = fromDatePicker!.Value.Date;
                    var toDate = toDatePicker!.Value.Date;

                    if (fromDate > toDate)
                    {
                        MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", 
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);
                    var dataList = weeklyData.ToList();

                    if (!dataList.Any())
                    {
                        MessageBox.Show("لا توجد بيانات للفترة المحددة", "تنبيه", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }

                    if (completeReportRadio!.Checked)
                    {
                        filePath = ExportService.ExportCompleteReportToCsv(dataList, fromDate, toDate);
                    }
                    else if (discountReportRadio!.Checked)
                    {
                        filePath = ExportService.ExportDiscountReportToCsv(dataList, fromDate, toDate);
                    }
                    else if (branchesReportRadio!.Checked)
                    {
                        filePath = ExportService.ExportBranchesReportToCsv(dataList, fromDate, toDate);
                    }
                    else if (monthlyReportRadio!.Checked)
                    {
                        filePath = ExportService.ExportMonthlyReportToCsv(dataList, fromDate);
                    }
                }

                LoadExportedFiles();

                var result = MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{filePath}\n\nهل تريد فتح الملف؟", 
                    "تم التصدير", MessageBoxButtons.YesNo, MessageBoxIcon.Information);

                if (result == DialogResult.Yes)
                {
                    System.Diagnostics.Process.Start("notepad.exe", filePath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في تصدير التقرير:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                exportButton!.Enabled = true;
                exportButton.Text = "تصدير التقرير";
            }
        }

        private void LoadExportedFiles()
        {
            try
            {
                var exportedFiles = ExportService.GetExportedFiles();
                exportedFilesGrid!.DataSource = exportedFiles;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الملفات المصدرة:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void OpenFileButton_Click(object? sender, EventArgs e)
        {
            if (GetSelectedExportFile() is ExportInfo selectedFile)
            {
                try
                {
                    System.Diagnostics.Process.Start("notepad.exe", selectedFile.FilePath);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"فشل في فتح الملف:\n{ex.Message}", 
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        private void DeleteFileButton_Click(object? sender, EventArgs e)
        {
            if (GetSelectedExportFile() is ExportInfo selectedFile)
            {
                var result = MessageBox.Show($"هل تريد حذف الملف؟\n{selectedFile.FileName}", 
                    "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    try
                    {
                        File.Delete(selectedFile.FilePath);
                        LoadExportedFiles();
                        
                        MessageBox.Show("تم حذف الملف بنجاح!", "نجح", 
                            MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"فشل في حذف الملف:\n{ex.Message}", 
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private ExportInfo? GetSelectedExportFile()
        {
            if (exportedFilesGrid?.SelectedRows.Count > 0)
            {
                return exportedFilesGrid.SelectedRows[0].DataBoundItem as ExportInfo;
            }
            return null;
        }
    }
}
