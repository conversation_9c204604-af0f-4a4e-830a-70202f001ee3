using System.ComponentModel;
using GashmiDebtManagement.Tests;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج اختبار النظام
    /// </summary>
    public partial class SystemTestsForm : Form
    {
        // عناصر التحكم
        private Panel? testPanel;
        private Button? runCalculationTestsButton;
        private Button? runIntegrationTestButton;
        private Button? runAllTestsButton;
        private TextBox? resultsTextBox;
        private ProgressBar? progressBar;
        private Label? statusLabel;

        public SystemTestsForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "اختبار النظام";
            this.Size = new Size(800, 600);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء لوحة الاختبارات
            CreateTestPanel();

            // إنشاء مربع النتائج
            CreateResultsTextBox();

            // شريط التقدم
            progressBar = new ProgressBar();
            progressBar.Height = 25;
            progressBar.Dock = DockStyle.Bottom;
            progressBar.Visible = false;

            // تسمية الحالة
            statusLabel = new Label();
            statusLabel.Text = "جاهز لتشغيل الاختبارات";
            statusLabel.Height = 30;
            statusLabel.Dock = DockStyle.Bottom;
            statusLabel.BackColor = Color.LightGray;
            statusLabel.TextAlign = ContentAlignment.MiddleCenter;
            statusLabel.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            this.Controls.AddRange(new Control[] {
                testPanel, resultsTextBox, progressBar, statusLabel
            });

            this.ResumeLayout(false);
        }

        private void CreateTestPanel()
        {
            testPanel = new Panel();
            testPanel.Height = 100;
            testPanel.Dock = DockStyle.Top;
            testPanel.BackColor = Color.LightBlue;

            var titleLabel = new Label();
            titleLabel.Text = "اختبارات النظام";
            titleLabel.Location = new Point(300, 10);
            titleLabel.Size = new Size(200, 30);
            titleLabel.Font = new Font("Tahoma", 14F, FontStyle.Bold);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.ForeColor = Color.DarkBlue;

            // زر اختبار العمليات الحسابية
            runCalculationTestsButton = new Button();
            runCalculationTestsButton.Text = "اختبار العمليات الحسابية";
            runCalculationTestsButton.Size = new Size(180, 35);
            runCalculationTestsButton.Location = new Point(550, 50);
            runCalculationTestsButton.BackColor = Color.LightGreen;
            runCalculationTestsButton.Click += RunCalculationTestsButton_Click;

            // زر اختبار التكامل
            runIntegrationTestButton = new Button();
            runIntegrationTestButton.Text = "اختبار التكامل";
            runIntegrationTestButton.Size = new Size(150, 35);
            runIntegrationTestButton.Location = new Point(390, 50);
            runIntegrationTestButton.BackColor = Color.LightYellow;
            runIntegrationTestButton.Click += RunIntegrationTestButton_Click;

            // زر تشغيل جميع الاختبارات
            runAllTestsButton = new Button();
            runAllTestsButton.Text = "تشغيل جميع الاختبارات";
            runAllTestsButton.Size = new Size(180, 35);
            runAllTestsButton.Location = new Point(200, 50);
            runAllTestsButton.BackColor = Color.LightCyan;
            runAllTestsButton.Click += RunAllTestsButton_Click;

            testPanel.Controls.AddRange(new Control[] {
                titleLabel, runCalculationTestsButton, runIntegrationTestButton, runAllTestsButton
            });
        }

        private void CreateResultsTextBox()
        {
            resultsTextBox = new TextBox();
            resultsTextBox.Dock = DockStyle.Fill;
            resultsTextBox.Multiline = true;
            resultsTextBox.ScrollBars = ScrollBars.Vertical;
            resultsTextBox.ReadOnly = true;
            resultsTextBox.BackColor = Color.White;
            resultsTextBox.Font = new Font("Consolas", 10F);
            resultsTextBox.Text = "اختر نوع الاختبار من الأزرار أعلاه...\n\n" +
                                 "الاختبارات المتاحة:\n" +
                                 "• اختبار العمليات الحسابية: يختبر صحة الحسابات والمعادلات\n" +
                                 "• اختبار التكامل: يختبر تكامل النظام مع بيانات تجريبية\n" +
                                 "• تشغيل جميع الاختبارات: يشغل جميع الاختبارات المتاحة\n\n" +
                                 "النتائج ستظهر هنا...";
        }

        private async void RunCalculationTestsButton_Click(object? sender, EventArgs e)
        {
            await RunTestsAsync("calculation");
        }

        private async void RunIntegrationTestButton_Click(object? sender, EventArgs e)
        {
            await RunTestsAsync("integration");
        }

        private async void RunAllTestsButton_Click(object? sender, EventArgs e)
        {
            await RunTestsAsync("all");
        }

        private async Task RunTestsAsync(string testType)
        {
            try
            {
                // تعطيل الأزرار
                SetButtonsEnabled(false);
                
                // إظهار شريط التقدم
                progressBar!.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                
                // مسح النتائج السابقة
                resultsTextBox!.Clear();
                
                // تحديث الحالة
                statusLabel!.Text = "جاري تشغيل الاختبارات...";
                statusLabel.ForeColor = Color.Blue;

                // تشغيل الاختبارات في مهمة منفصلة
                await Task.Run(() =>
                {
                    // إعادة توجيه الإخراج إلى مربع النص
                    var originalOut = Console.Out;
                    var stringWriter = new StringWriter();
                    Console.SetOut(stringWriter);

                    try
                    {
                        switch (testType)
                        {
                            case "calculation":
                                CalculationTests.RunAllTests();
                                break;
                            case "integration":
                                CalculationTests.RunIntegrationTest();
                                break;
                            case "all":
                                CalculationTests.RunAllTests();
                                CalculationTests.RunIntegrationTest();
                                break;
                        }

                        // الحصول على النتائج
                        var results = stringWriter.ToString();
                        
                        // تحديث واجهة المستخدم في الخيط الرئيسي
                        this.Invoke(new Action(() =>
                        {
                            resultsTextBox!.Text = results;
                            statusLabel!.Text = "تم إكمال الاختبارات";
                            statusLabel.ForeColor = Color.Green;
                        }));
                    }
                    finally
                    {
                        // استعادة الإخراج الأصلي
                        Console.SetOut(originalOut);
                    }
                });
            }
            catch (Exception ex)
            {
                resultsTextBox!.Text = $"خطأ في تشغيل الاختبارات:\n{ex.Message}";
                statusLabel!.Text = "فشل في تشغيل الاختبارات";
                statusLabel.ForeColor = Color.Red;
            }
            finally
            {
                // إخفاء شريط التقدم
                progressBar!.Visible = false;
                
                // تفعيل الأزرار
                SetButtonsEnabled(true);
            }
        }

        private void SetButtonsEnabled(bool enabled)
        {
            runCalculationTestsButton!.Enabled = enabled;
            runIntegrationTestButton!.Enabled = enabled;
            runAllTestsButton!.Enabled = enabled;
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            
            // إضافة معلومات إضافية
            resultsTextBox!.Text += "\n\n" +
                                   "معلومات النظام:\n" +
                                   $"إصدار .NET: {Environment.Version}\n" +
                                   $"نظام التشغيل: {Environment.OSVersion}\n" +
                                   $"المعالج: {Environment.ProcessorCount} cores\n" +
                                   $"الذاكرة المستخدمة: {GC.GetTotalMemory(false) / 1024 / 1024} MB\n" +
                                   $"وقت التشغيل: {Environment.TickCount / 1000} seconds\n";
        }
    }
}
