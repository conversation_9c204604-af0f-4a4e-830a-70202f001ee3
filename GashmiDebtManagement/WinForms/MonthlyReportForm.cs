using GashmiDebtManagement.Repositories;

namespace GashmiDebtManagement.WinForms
{
    public partial class MonthlyReportForm : Form
    {
        private readonly IWeeklyDebtsRepository _repository;

        public MonthlyReportForm(IWeeklyDebtsRepository repository)
        {
            _repository = repository;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "التقرير الشهري";
            this.Size = new Size(800, 600);
            this.RightToLeft = RightToLeft.Yes;
            this.Font = new Font("Tahoma", 10F);

            var label = new Label();
            label.Text = "التقرير الشهري - قيد التطوير";
            label.Dock = DockStyle.Fill;
            label.TextAlign = ContentAlignment.MiddleCenter;
            label.Font = new Font("Tahoma", 16F);

            this.Controls.Add(label);
        }
    }
}
