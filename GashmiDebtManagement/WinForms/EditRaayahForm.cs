using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج تعديل بيانات الرعوي
    /// </summary>
    public partial class EditRaayahForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        private readonly Raayah _raayah;

        // عناصر التحكم
        private Label? nameLabel;
        private TextBox? nameTextBox;
        private CheckBox? enableDiscountCheckBox;
        private CheckBox? inKashfOzriCheckBox;
        private CheckBox? inKharijKashfCheckBox;
        private Label? createdDateLabel;
        private Label? createdDateValue;
        private Button? saveButton;
        private Button? cancelButton;
        private Panel? buttonPanel;

        // الأحداث
        public event EventHandler<RaayahEventArgs>? RaayahUpdated;

        public EditRaayahForm(IRaayahRepository raayahRepository, Raayah raayah)
        {
            _raayahRepository = raayahRepository;
            _raayah = raayah;
            InitializeComponent();
            LoadRaayahData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "تعديل بيانات الرعوي";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء عناصر التحكم
            CreateControls();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void CreateControls()
        {
            // تسمية الاسم
            nameLabel = new Label();
            nameLabel.Text = "الاسم الكامل:";
            nameLabel.Location = new Point(350, 30);
            nameLabel.Size = new Size(100, 23);
            nameLabel.TextAlign = ContentAlignment.MiddleRight;

            // مربع نص الاسم
            nameTextBox = new TextBox();
            nameTextBox.Location = new Point(50, 30);
            nameTextBox.Size = new Size(290, 23);
            nameTextBox.TextChanged += NameTextBox_TextChanged;

            // مربع اختيار خصم الحوالة
            enableDiscountCheckBox = new CheckBox();
            enableDiscountCheckBox.Text = "تفعيل خصم الحوالة (3%)";
            enableDiscountCheckBox.Location = new Point(200, 80);
            enableDiscountCheckBox.Size = new Size(200, 23);

            // مربع اختيار كشف الأوزري
            inKashfOzriCheckBox = new CheckBox();
            inKashfOzriCheckBox.Text = "إدراج في كشف الأوزري";
            inKashfOzriCheckBox.Location = new Point(200, 120);
            inKashfOzriCheckBox.Size = new Size(200, 23);

            // مربع اختيار خارج الكشف
            inKharijKashfCheckBox = new CheckBox();
            inKharijKashfCheckBox.Text = "إدراج في خارج الكشف";
            inKharijKashfCheckBox.Location = new Point(200, 160);
            inKharijKashfCheckBox.Size = new Size(200, 23);

            // تسمية تاريخ الإنشاء
            createdDateLabel = new Label();
            createdDateLabel.Text = "تاريخ الإنشاء:";
            createdDateLabel.Location = new Point(350, 200);
            createdDateLabel.Size = new Size(100, 23);
            createdDateLabel.TextAlign = ContentAlignment.MiddleRight;

            // قيمة تاريخ الإنشاء
            createdDateValue = new Label();
            createdDateValue.Location = new Point(50, 200);
            createdDateValue.Size = new Size(290, 23);
            createdDateValue.BackColor = Color.LightGray;
            createdDateValue.BorderStyle = BorderStyle.Fixed3D;
            createdDateValue.TextAlign = ContentAlignment.MiddleLeft;

            // لوحة الأزرار
            buttonPanel = new Panel();
            buttonPanel.Height = 60;
            buttonPanel.Dock = DockStyle.Bottom;
            buttonPanel.BackColor = Color.LightGray;

            // زر الحفظ
            saveButton = new Button();
            saveButton.Text = "حفظ التعديلات";
            saveButton.Size = new Size(120, 35);
            saveButton.Location = new Point(280, 12);
            saveButton.BackColor = Color.LightGreen;
            saveButton.Click += SaveButton_Click;

            // زر الإلغاء
            cancelButton = new Button();
            cancelButton.Text = "إلغاء";
            cancelButton.Size = new Size(100, 35);
            cancelButton.Location = new Point(170, 12);
            cancelButton.BackColor = Color.LightCoral;
            cancelButton.Click += CancelButton_Click;

            // إضافة الأزرار للوحة
            buttonPanel.Controls.AddRange(new Control[] { saveButton, cancelButton });

            // إضافة جميع العناصر للنموذج
            this.Controls.AddRange(new Control[] {
                nameLabel, nameTextBox,
                enableDiscountCheckBox, inKashfOzriCheckBox, inKharijKashfCheckBox,
                createdDateLabel, createdDateValue,
                buttonPanel
            });
        }

        private void LoadRaayahData()
        {
            // تحميل بيانات الرعوي الحالية
            nameTextBox!.Text = _raayah.FullName;
            enableDiscountCheckBox!.Checked = _raayah.EnableDiscount;
            inKashfOzriCheckBox!.Checked = _raayah.InKashfOzri;
            inKharijKashfCheckBox!.Checked = _raayah.InKharijKashf;
            createdDateValue!.Text = _raayah.CreatedDate.ToString("dd/MM/yyyy HH:mm");

            // تعيين العنوان
            this.Text = $"تعديل بيانات الرعوي: {_raayah.FullName}";
        }

        private void NameTextBox_TextChanged(object? sender, EventArgs e)
        {
            // تفعيل زر الحفظ فقط إذا تم إدخال اسم
            saveButton!.Enabled = !string.IsNullOrWhiteSpace(nameTextBox?.Text);
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                var fullName = nameTextBox!.Text.Trim();

                // التحقق من عدم تكرار الاسم (باستثناء الرعوي الحالي)
                if (fullName != _raayah.FullName && await _raayahRepository.IsNameExistsAsync(fullName, _raayah.Id))
                {
                    MessageBox.Show("هذا الاسم موجود مسبقاً! يرجى اختيار اسم آخر.",
                        "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    nameTextBox.Focus();
                    return;
                }

                // تحديث بيانات الرعوي
                _raayah.FullName = fullName;
                _raayah.EnableDiscount = enableDiscountCheckBox!.Checked;
                _raayah.InKashfOzri = inKashfOzriCheckBox!.Checked;
                _raayah.InKharijKashf = inKharijKashfCheckBox!.Checked;

                // حفظ التعديلات في قاعدة البيانات
                await _raayahRepository.UpdateAsync(_raayah);
                await _raayahRepository.SaveChangesAsync();

                // إثارة الحدث
                RaayahUpdated?.Invoke(this, new RaayahEventArgs(_raayah));

                // رسالة نجاح
                MessageBox.Show("تم حفظ التعديلات بنجاح!", "نجح",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // إغلاق النموذج
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في حفظ التعديلات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object? sender, EventArgs e)
        {
            // التحقق من وجود تعديلات غير محفوظة
            if (HasUnsavedChanges())
            {
                var result = MessageBox.Show("هناك تعديلات غير محفوظة. هل تريد المتابعة؟",
                    "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                    return;
            }

            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateInput()
        {
            // التحقق من الاسم
            if (string.IsNullOrWhiteSpace(nameTextBox?.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox?.Focus();
                return false;
            }

            // التحقق من طول الاسم
            if (nameTextBox.Text.Trim().Length < 3)
            {
                MessageBox.Show("الاسم يجب أن يكون 3 أحرف على الأقل", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }

            if (nameTextBox.Text.Trim().Length > 100)
            {
                MessageBox.Show("الاسم يجب أن يكون أقل من 100 حرف", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }

            return true;
        }

        private bool HasUnsavedChanges()
        {
            // التحقق من وجود تعديلات
            return nameTextBox!.Text.Trim() != _raayah.FullName ||
                   enableDiscountCheckBox!.Checked != _raayah.EnableDiscount ||
                   inKashfOzriCheckBox!.Checked != _raayah.InKashfOzri ||
                   inKharijKashfCheckBox!.Checked != _raayah.InKharijKashf;
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);

            // تعيين التركيز على مربع النص
            nameTextBox?.Focus();
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            // معالجة اختصارات لوحة المفاتيح
            switch (keyData)
            {
                case Keys.Enter:
                    if (saveButton?.Enabled == true)
                    {
                        SaveButton_Click(null, EventArgs.Empty);
                        return true;
                    }
                    break;

                case Keys.Escape:
                    CancelButton_Click(null, EventArgs.Empty);
                    return true;
            }

            return base.ProcessCmdKey(ref msg, keyData);
        }

        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            // التحقق من التعديلات غير المحفوظة عند الإغلاق
            if (this.DialogResult != DialogResult.OK && HasUnsavedChanges())
            {
                var result = MessageBox.Show("هناك تعديلات غير محفوظة. هل تريد المتابعة؟",
                    "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.No)
                {
                    e.Cancel = true;
                }
            }

            base.OnFormClosing(e);
        }
    }
}
