using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج إدخال البيانات الأسبوعية
    /// </summary>
    public partial class WeeklyDataEntryForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;
        
        // عناصر التحكم
        private Panel? datePanel;
        private Label? fromDateLabel;
        private DateTimePicker? fromDatePicker;
        private Label? toDateLabel;
        private DateTimePicker? toDatePicker;
        private Button? loadRaayahButton;
        private DataGridView? dataGridView;
        private Panel? summaryPanel;
        private Label? summaryLabel;
        private Button? saveButton;
        private Button? clearButton;
        private ProgressBar? progressBar;

        public WeeklyDataEntryForm(IRaayahRepository raayahRepository, IWeeklyDebtsRepository weeklyDebtsRepository)
        {
            _raayahRepository = raayahRepository;
            _weeklyDebtsRepository = weeklyDebtsRepository;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "إدخال البيانات الأسبوعية";
            this.Size = new Size(1200, 700);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء لوحة التاريخ
            CreateDatePanel();

            // إنشاء جدول البيانات
            CreateDataGridView();

            // إنشاء لوحة الملخص
            CreateSummaryPanel();

            this.ResumeLayout(false);
        }

        private void CreateDatePanel()
        {
            datePanel = new Panel();
            datePanel.Height = 80;
            datePanel.Dock = DockStyle.Top;
            datePanel.BackColor = Color.LightBlue;

            // تسمية تاريخ البداية
            fromDateLabel = new Label();
            fromDateLabel.Text = "من تاريخ:";
            fromDateLabel.Location = new Point(1000, 25);
            fromDateLabel.Size = new Size(80, 23);
            fromDateLabel.TextAlign = ContentAlignment.MiddleRight;

            // منتقي تاريخ البداية
            fromDatePicker = new DateTimePicker();
            fromDatePicker.Location = new Point(850, 25);
            fromDatePicker.Size = new Size(140, 23);
            fromDatePicker.Format = DateTimePickerFormat.Short;
            fromDatePicker.ValueChanged += FromDatePicker_ValueChanged;

            // تسمية تاريخ النهاية
            toDateLabel = new Label();
            toDateLabel.Text = "إلى تاريخ:";
            toDateLabel.Location = new Point(750, 25);
            toDateLabel.Size = new Size(80, 23);
            toDateLabel.TextAlign = ContentAlignment.MiddleRight;

            // منتقي تاريخ النهاية
            toDatePicker = new DateTimePicker();
            toDatePicker.Location = new Point(600, 25);
            toDatePicker.Size = new Size(140, 23);
            toDatePicker.Format = DateTimePickerFormat.Short;
            toDatePicker.Enabled = false; // يتم حسابه تلقائياً

            // زر تحميل الرعية
            loadRaayahButton = new Button();
            loadRaayahButton.Text = "تحميل قائمة الرعية";
            loadRaayahButton.Size = new Size(150, 35);
            loadRaayahButton.Location = new Point(400, 20);
            loadRaayahButton.BackColor = Color.LightGreen;
            loadRaayahButton.Click += LoadRaayahButton_Click;

            // شريط التقدم
            progressBar = new ProgressBar();
            progressBar.Location = new Point(50, 50);
            progressBar.Size = new Size(300, 20);
            progressBar.Visible = false;

            datePanel.Controls.AddRange(new Control[] {
                fromDateLabel, fromDatePicker, toDateLabel, toDatePicker,
                loadRaayahButton, progressBar
            });

            this.Controls.Add(datePanel);
        }

        private void CreateDataGridView()
        {
            dataGridView = new DataGridView();
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.AutoGenerateColumns = false;
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.BackgroundColor = Color.White;
            dataGridView.Font = new Font("Tahoma", 9F);

            // إعداد الأعمدة
            SetupDataGridViewColumns();

            // أحداث
            dataGridView.CellValueChanged += DataGridView_CellValueChanged;
            dataGridView.CellEndEdit += DataGridView_CellEndEdit;

            this.Controls.Add(dataGridView);
        }

        private void SetupDataGridViewColumns()
        {
            // عمود الاسم (للقراءة فقط)
            var nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "RaayahName";
            nameColumn.HeaderText = "اسم الرعوي";
            nameColumn.Width = 200;
            nameColumn.ReadOnly = true;

            // أعمدة الفروع
            var samirColumn = new DataGridViewTextBoxColumn();
            samirColumn.Name = "SamirAmount";
            samirColumn.HeaderText = "سمير";
            samirColumn.Width = 100;
            samirColumn.DefaultCellStyle.Format = "N2";

            var maherColumn = new DataGridViewTextBoxColumn();
            maherColumn.Name = "MaherAmount";
            maherColumn.HeaderText = "ماهر";
            maherColumn.Width = 100;
            maherColumn.DefaultCellStyle.Format = "N2";

            var raidColumn = new DataGridViewTextBoxColumn();
            raidColumn.Name = "RaidAmount";
            raidColumn.HeaderText = "رايد";
            raidColumn.Width = 100;
            raidColumn.DefaultCellStyle.Format = "N2";

            var haiderColumn = new DataGridViewTextBoxColumn();
            haiderColumn.Name = "HaiderAmount";
            haiderColumn.HeaderText = "حيدر";
            haiderColumn.Width = 100;
            haiderColumn.DefaultCellStyle.Format = "N2";

            var lateColumn = new DataGridViewTextBoxColumn();
            lateColumn.Name = "LateAmount";
            lateColumn.HeaderText = "متأخر";
            lateColumn.Width = 100;
            lateColumn.DefaultCellStyle.Format = "N2";

            var receivedColumn = new DataGridViewTextBoxColumn();
            receivedColumn.Name = "ReceivedAmount";
            receivedColumn.HeaderText = "واصل";
            receivedColumn.Width = 100;
            receivedColumn.DefaultCellStyle.Format = "N2";

            // أعمدة محسوبة (للقراءة فقط)
            var totalColumn = new DataGridViewTextBoxColumn();
            totalColumn.Name = "TotalAmount";
            totalColumn.HeaderText = "الإجمالي";
            totalColumn.Width = 120;
            totalColumn.ReadOnly = true;
            totalColumn.DefaultCellStyle.Format = "N2";
            totalColumn.DefaultCellStyle.BackColor = Color.LightYellow;

            var discountColumn = new DataGridViewTextBoxColumn();
            discountColumn.Name = "DiscountAmount";
            discountColumn.HeaderText = "الخصم";
            discountColumn.Width = 100;
            discountColumn.ReadOnly = true;
            discountColumn.DefaultCellStyle.Format = "N2";
            discountColumn.DefaultCellStyle.BackColor = Color.LightYellow;

            var netColumn = new DataGridViewTextBoxColumn();
            netColumn.Name = "NetAmount";
            netColumn.HeaderText = "الصافي";
            netColumn.Width = 120;
            netColumn.ReadOnly = true;
            netColumn.DefaultCellStyle.Format = "N2";
            netColumn.DefaultCellStyle.BackColor = Color.LightGreen;

            dataGridView!.Columns.AddRange(new DataGridViewColumn[] {
                nameColumn, samirColumn, maherColumn, raidColumn, haiderColumn,
                lateColumn, receivedColumn, totalColumn, discountColumn, netColumn
            });

            // تنسيق الرؤوس
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Navy;
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            dataGridView.ColumnHeadersHeight = 35;

            // تنسيق الصفوف
            dataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.AliceBlue;
            dataGridView.RowsDefaultCellStyle.BackColor = Color.White;
            dataGridView.RowTemplate.Height = 30;
        }

        private void CreateSummaryPanel()
        {
            summaryPanel = new Panel();
            summaryPanel.Height = 100;
            summaryPanel.Dock = DockStyle.Bottom;
            summaryPanel.BackColor = Color.LightGray;

            // تسمية الملخص
            summaryLabel = new Label();
            summaryLabel.Text = "الملخص: لم يتم تحميل البيانات بعد";
            summaryLabel.Location = new Point(20, 10);
            summaryLabel.Size = new Size(800, 50);
            summaryLabel.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            // زر الحفظ
            saveButton = new Button();
            saveButton.Text = "حفظ البيانات";
            saveButton.Size = new Size(120, 40);
            saveButton.Location = new Point(950, 30);
            saveButton.BackColor = Color.LightGreen;
            saveButton.Enabled = false;
            saveButton.Click += SaveButton_Click;

            // زر المسح
            clearButton = new Button();
            clearButton.Text = "مسح الكل";
            clearButton.Size = new Size(100, 40);
            clearButton.Location = new Point(840, 30);
            clearButton.BackColor = Color.LightCoral;
            clearButton.Enabled = false;
            clearButton.Click += ClearButton_Click;

            summaryPanel.Controls.AddRange(new Control[] {
                summaryLabel, saveButton, clearButton
            });

            this.Controls.Add(summaryPanel);
        }

        private void FromDatePicker_ValueChanged(object? sender, EventArgs e)
        {
            // حساب تاريخ النهاية تلقائياً (بعد 6 أيام)
            toDatePicker!.Value = fromDatePicker!.Value.AddDays(6);
        }

        private async void LoadRaayahButton_Click(object? sender, EventArgs e)
        {
            try
            {
                // إظهار شريط التقدم
                progressBar!.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                loadRaayahButton!.Enabled = false;

                // تحميل قائمة الرعية
                var raayahList = await _raayahRepository.GetAllAsync();
                var raayahArray = raayahList.OrderBy(r => r.FullName).ToArray();

                if (!raayahArray.Any())
                {
                    MessageBox.Show("لا توجد رعية مسجلة! يرجى إضافة رعية أولاً.", 
                        "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // التحقق من وجود بيانات لهذه الفترة
                var fromDate = fromDatePicker!.Value.Date;
                var toDate = toDatePicker!.Value.Date;
                var existingData = await _weeklyDebtsRepository.GetByPeriodAsync(fromDate, toDate);

                if (existingData.Any())
                {
                    var result = MessageBox.Show(
                        "يوجد بيانات مسجلة لهذه الفترة! هل تريد تحميلها للتعديل؟",
                        "بيانات موجودة", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                    if (result == DialogResult.Cancel)
                        return;
                    else if (result == DialogResult.Yes)
                    {
                        LoadExistingData(raayahArray, existingData);
                        return;
                    }
                }

                // إنشاء صفوف جديدة
                CreateNewDataRows(raayahArray, fromDate, toDate);

                // تفعيل الأزرار
                saveButton!.Enabled = true;
                clearButton!.Enabled = true;

                UpdateSummary();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // إخفاء شريط التقدم
                progressBar!.Visible = false;
                loadRaayahButton!.Enabled = true;
            }
        }

        private void CreateNewDataRows(Raayah[] raayahArray, DateTime fromDate, DateTime toDate)
        {
            var dataRows = new List<WeeklyDataRow>();

            foreach (var raayah in raayahArray)
            {
                dataRows.Add(new WeeklyDataRow
                {
                    RaayahId = raayah.Id,
                    RaayahName = raayah.FullName,
                    EnableDiscount = raayah.EnableDiscount,
                    DateFrom = fromDate,
                    DateTo = toDate
                });
            }

            dataGridView!.DataSource = dataRows;
        }

        private void LoadExistingData(Raayah[] raayahArray, IEnumerable<WeeklyDebts> existingData)
        {
            var dataRows = new List<WeeklyDataRow>();
            var existingDict = existingData.ToDictionary(d => d.RaayahId);

            foreach (var raayah in raayahArray)
            {
                var row = new WeeklyDataRow
                {
                    RaayahId = raayah.Id,
                    RaayahName = raayah.FullName,
                    EnableDiscount = raayah.EnableDiscount,
                    DateFrom = fromDatePicker!.Value.Date,
                    DateTo = toDatePicker!.Value.Date
                };

                if (existingDict.TryGetValue(raayah.Id, out var existingDebt))
                {
                    row.SamirAmount = existingDebt.SamirAmount;
                    row.MaherAmount = existingDebt.MaherAmount;
                    row.RaidAmount = existingDebt.RaidAmount;
                    row.HaiderAmount = existingDebt.HaiderAmount;
                    row.LateAmount = existingDebt.LateAmount;
                    row.ReceivedAmount = existingDebt.ReceivedAmount;
                }

                dataRows.Add(row);
            }

            dataGridView!.DataSource = dataRows;
        }

        private void DataGridView_CellValueChanged(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                UpdateCalculatedColumns(e.RowIndex);
                UpdateSummary();
            }
        }

        private void DataGridView_CellEndEdit(object? sender, DataGridViewCellEventArgs e)
        {
            // التحقق من صحة القيم المدخلة
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var cell = dataGridView!.Rows[e.RowIndex].Cells[e.ColumnIndex];
                if (cell.Value != null && !decimal.TryParse(cell.Value.ToString(), out _))
                {
                    MessageBox.Show("يرجى إدخال رقم صحيح", "خطأ في الإدخال", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cell.Value = 0;
                }
            }
        }

        private void UpdateCalculatedColumns(int rowIndex)
        {
            var row = dataGridView!.Rows[rowIndex];
            
            // الحصول على القيم
            var samir = GetDecimalValue(row.Cells["SamirAmount"].Value);
            var maher = GetDecimalValue(row.Cells["MaherAmount"].Value);
            var raid = GetDecimalValue(row.Cells["RaidAmount"].Value);
            var haider = GetDecimalValue(row.Cells["HaiderAmount"].Value);
            var late = GetDecimalValue(row.Cells["LateAmount"].Value);
            var received = GetDecimalValue(row.Cells["ReceivedAmount"].Value);

            // حساب الإجمالي
            var total = samir + maher + raid + haider + late;
            row.Cells["TotalAmount"].Value = total;

            // حساب الخصم
            var dataRow = dataGridView.Rows[rowIndex].DataBoundItem as WeeklyDataRow;
            var discount = dataRow?.EnableDiscount == true ? (total - received) * 0.03m : 0;
            row.Cells["DiscountAmount"].Value = discount;

            // حساب الصافي
            var net = total - received - discount;
            row.Cells["NetAmount"].Value = net;
        }

        private decimal GetDecimalValue(object? value)
        {
            if (value == null || value == DBNull.Value)
                return 0;
            
            if (decimal.TryParse(value.ToString(), out decimal result))
                return result;
            
            return 0;
        }

        private void UpdateSummary()
        {
            if (dataGridView?.DataSource == null)
                return;

            var rows = dataGridView.DataSource as List<WeeklyDataRow>;
            if (rows == null || !rows.Any())
                return;

            var totalDebts = 0m;
            var totalReceived = 0m;
            var totalDiscount = 0m;
            var totalNet = 0m;

            foreach (DataGridViewRow row in dataGridView.Rows)
            {
                totalDebts += GetDecimalValue(row.Cells["TotalAmount"].Value);
                totalReceived += GetDecimalValue(row.Cells["ReceivedAmount"].Value);
                totalDiscount += GetDecimalValue(row.Cells["DiscountAmount"].Value);
                totalNet += GetDecimalValue(row.Cells["NetAmount"].Value);
            }

            summaryLabel!.Text = $"الملخص - إجمالي الديون: {totalDebts:N2} | " +
                                $"إجمالي الواصل: {totalReceived:N2} | " +
                                $"إجمالي الخصومات: {totalDiscount:N2} | " +
                                $"الصافي: {totalNet:N2}";
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var result = MessageBox.Show("هل أنت متأكد من حفظ البيانات؟", 
                    "تأكيد الحفظ", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                    return;

                // إظهار شريط التقدم
                progressBar!.Visible = true;
                progressBar.Style = ProgressBarStyle.Marquee;
                saveButton!.Enabled = false;

                var fromDate = fromDatePicker!.Value.Date;
                var toDate = toDatePicker!.Value.Date;

                // حذف البيانات الموجودة لنفس الفترة
                await _weeklyDebtsRepository.DeleteByPeriodAsync(fromDate, toDate);

                // إنشاء البيانات الجديدة
                var weeklyDebts = new List<WeeklyDebts>();

                foreach (DataGridViewRow row in dataGridView!.Rows)
                {
                    var dataRow = row.DataBoundItem as WeeklyDataRow;
                    if (dataRow == null) continue;

                    var debt = new WeeklyDebts
                    {
                        RaayahId = dataRow.RaayahId,
                        DateFrom = fromDate,
                        DateTo = toDate,
                        SamirAmount = GetDecimalValue(row.Cells["SamirAmount"].Value),
                        MaherAmount = GetDecimalValue(row.Cells["MaherAmount"].Value),
                        RaidAmount = GetDecimalValue(row.Cells["RaidAmount"].Value),
                        HaiderAmount = GetDecimalValue(row.Cells["HaiderAmount"].Value),
                        LateAmount = GetDecimalValue(row.Cells["LateAmount"].Value),
                        ReceivedAmount = GetDecimalValue(row.Cells["ReceivedAmount"].Value)
                    };

                    weeklyDebts.Add(debt);
                }

                // حفظ البيانات
                await _weeklyDebtsRepository.AddRangeAsync(weeklyDebts);
                await _weeklyDebtsRepository.SaveChangesAsync();

                MessageBox.Show("تم حفظ البيانات بنجاح!", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في حفظ البيانات:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                progressBar!.Visible = false;
                saveButton!.Enabled = true;
            }
        }

        private void ClearButton_Click(object? sender, EventArgs e)
        {
            var result = MessageBox.Show("هل أنت متأكد من مسح جميع البيانات؟", 
                "تأكيد المسح", MessageBoxButtons.YesNo, MessageBoxIcon.Warning);

            if (result == DialogResult.Yes)
            {
                dataGridView!.DataSource = null;
                saveButton!.Enabled = false;
                clearButton!.Enabled = false;
                summaryLabel!.Text = "الملخص: لم يتم تحميل البيانات بعد";
            }
        }
    }

    /// <summary>
    /// فئة مساعدة لصف البيانات الأسبوعية
    /// </summary>
    public class WeeklyDataRow
    {
        public int RaayahId { get; set; }
        public string RaayahName { get; set; } = string.Empty;
        public bool EnableDiscount { get; set; }
        public DateTime DateFrom { get; set; }
        public DateTime DateTo { get; set; }
        public decimal SamirAmount { get; set; }
        public decimal MaherAmount { get; set; }
        public decimal RaidAmount { get; set; }
        public decimal HaiderAmount { get; set; }
        public decimal LateAmount { get; set; }
        public decimal ReceivedAmount { get; set; }
    }
}
