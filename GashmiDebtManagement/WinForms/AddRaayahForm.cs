using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج إضافة رعوي جديد
    /// </summary>
    public partial class AddRaayahForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        
        // عناصر التحكم
        private Label? nameLabel;
        private TextBox? nameTextBox;
        private CheckBox? enableDiscountCheckBox;
        private CheckBox? inKashfOzriCheckBox;
        private CheckBox? inKharijKashfCheckBox;
        private Button? saveButton;
        private Button? cancelButton;
        private Panel? buttonPanel;

        // الأحداث
        public event EventHandler<RaayahEventArgs>? RaayahAdded;

        public AddRaayahForm(IRaayahRepository raayahRepository)
        {
            _raayahRepository = raayahRepository;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "إضافة رعوي جديد";
            this.Size = new Size(500, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء عناصر التحكم
            CreateControls();

            this.ResumeLayout(false);
            this.PerformLayout();
        }

        private void CreateControls()
        {
            // تسمية الاسم
            nameLabel = new Label();
            nameLabel.Text = "الاسم الكامل:";
            nameLabel.Location = new Point(350, 30);
            nameLabel.Size = new Size(100, 23);
            nameLabel.TextAlign = ContentAlignment.MiddleRight;

            // مربع نص الاسم
            nameTextBox = new TextBox();
            nameTextBox.Location = new Point(50, 30);
            nameTextBox.Size = new Size(290, 23);
            nameTextBox.TextChanged += NameTextBox_TextChanged;

            // مربع اختيار خصم الحوالة
            enableDiscountCheckBox = new CheckBox();
            enableDiscountCheckBox.Text = "تفعيل خصم الحوالة (3%)";
            enableDiscountCheckBox.Location = new Point(200, 80);
            enableDiscountCheckBox.Size = new Size(200, 23);
            enableDiscountCheckBox.Checked = true; // افتراضياً مفعل

            // مربع اختيار كشف الأوزري
            inKashfOzriCheckBox = new CheckBox();
            inKashfOzriCheckBox.Text = "إدراج في كشف الأوزري";
            inKashfOzriCheckBox.Location = new Point(200, 120);
            inKashfOzriCheckBox.Size = new Size(200, 23);
            inKashfOzriCheckBox.Checked = true; // افتراضياً مفعل

            // مربع اختيار خارج الكشف
            inKharijKashfCheckBox = new CheckBox();
            inKharijKashfCheckBox.Text = "إدراج في خارج الكشف";
            inKharijKashfCheckBox.Location = new Point(200, 160);
            inKharijKashfCheckBox.Size = new Size(200, 23);
            inKharijKashfCheckBox.Checked = false; // افتراضياً معطل

            // لوحة الأزرار
            buttonPanel = new Panel();
            buttonPanel.Height = 60;
            buttonPanel.Dock = DockStyle.Bottom;
            buttonPanel.BackColor = Color.LightGray;

            // زر الحفظ
            saveButton = new Button();
            saveButton.Text = "حفظ";
            saveButton.Size = new Size(100, 35);
            saveButton.Location = new Point(280, 12);
            saveButton.BackColor = Color.LightGreen;
            saveButton.Enabled = false; // معطل حتى يتم إدخال اسم
            saveButton.Click += SaveButton_Click;

            // زر الإلغاء
            cancelButton = new Button();
            cancelButton.Text = "إلغاء";
            cancelButton.Size = new Size(100, 35);
            cancelButton.Location = new Point(170, 12);
            cancelButton.BackColor = Color.LightCoral;
            cancelButton.Click += CancelButton_Click;

            // إضافة الأزرار للوحة
            buttonPanel.Controls.AddRange(new Control[] { saveButton, cancelButton });

            // إضافة جميع العناصر للنموذج
            this.Controls.AddRange(new Control[] {
                nameLabel, nameTextBox,
                enableDiscountCheckBox, inKashfOzriCheckBox, inKharijKashfCheckBox,
                buttonPanel
            });

            // تعيين التركيز على مربع النص
            nameTextBox.Focus();
        }

        private void NameTextBox_TextChanged(object? sender, EventArgs e)
        {
            // تفعيل زر الحفظ فقط إذا تم إدخال اسم
            saveButton!.Enabled = !string.IsNullOrWhiteSpace(nameTextBox?.Text);
        }

        private async void SaveButton_Click(object? sender, EventArgs e)
        {
            try
            {
                // التحقق من صحة البيانات
                if (!ValidateInput())
                    return;

                var fullName = nameTextBox!.Text.Trim();

                // التحقق من عدم تكرار الاسم
                if (await _raayahRepository.IsNameExistsAsync(fullName))
                {
                    MessageBox.Show("هذا الاسم موجود مسبقاً! يرجى اختيار اسم آخر.", 
                        "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    nameTextBox.Focus();
                    return;
                }

                // إنشاء رعوي جديد
                var newRaayah = new Raayah
                {
                    FullName = fullName,
                    EnableDiscount = enableDiscountCheckBox!.Checked,
                    InKashfOzri = inKashfOzriCheckBox!.Checked,
                    InKharijKashf = inKharijKashfCheckBox!.Checked,
                    CreatedDate = DateTime.Now
                };

                // حفظ في قاعدة البيانات
                await _raayahRepository.AddAsync(newRaayah);
                await _raayahRepository.SaveChangesAsync();

                // إثارة الحدث
                RaayahAdded?.Invoke(this, new RaayahEventArgs(newRaayah));

                // رسالة نجاح
                MessageBox.Show("تم إضافة الرعوي بنجاح!", "نجح", 
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // إغلاق النموذج
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في إضافة الرعوي:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CancelButton_Click(object? sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private bool ValidateInput()
        {
            // التحقق من الاسم
            if (string.IsNullOrWhiteSpace(nameTextBox?.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل", "تحذير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox?.Focus();
                return false;
            }

            // التحقق من طول الاسم
            if (nameTextBox.Text.Trim().Length < 3)
            {
                MessageBox.Show("الاسم يجب أن يكون 3 أحرف على الأقل", "تحذير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }

            if (nameTextBox.Text.Trim().Length > 100)
            {
                MessageBox.Show("الاسم يجب أن يكون أقل من 100 حرف", "تحذير", 
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                nameTextBox.Focus();
                return false;
            }

            return true;
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            
            // تعيين التركيز على مربع النص
            nameTextBox?.Focus();
        }

        protected override bool ProcessCmdKey(ref Message msg, Keys keyData)
        {
            // معالجة اختصارات لوحة المفاتيح
            switch (keyData)
            {
                case Keys.Enter:
                    if (saveButton?.Enabled == true)
                    {
                        SaveButton_Click(null, EventArgs.Empty);
                        return true;
                    }
                    break;
                    
                case Keys.Escape:
                    CancelButton_Click(null, EventArgs.Empty);
                    return true;
            }

            return base.ProcessCmdKey(ref msg, keyData);
        }
    }
}
