using System.ComponentModel;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Repositories;
using GashmiDebtManagement.Services;

namespace GashmiDebtManagement.WinForms
{
    /// <summary>
    /// نموذج التقرير الكامل
    /// </summary>
    public partial class CompleteReportForm : Form
    {
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;
        
        // عناصر التحكم
        private Panel? filterPanel;
        private Label? titleLabel;
        private Label? fromDateLabel;
        private DateTimePicker? fromDatePicker;
        private Label? toDateLabel;
        private DateTimePicker? toDatePicker;
        private Button? generateButton;
        private Button? exportButton;
        private Button? printButton;
        private DataGridView? reportGridView;
        private Panel? summaryPanel;
        private Label? summaryLabel;
        private Label? periodLabel;

        public CompleteReportForm(IWeeklyDebtsRepository weeklyDebtsRepository)
        {
            _weeklyDebtsRepository = weeklyDebtsRepository;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إعدادات النموذج
            this.Text = "التقرير الكامل";
            this.Size = new Size(1400, 800);
            this.RightToLeft = RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.Font = new Font("Tahoma", 10F);

            // إنشاء لوحة التصفية
            CreateFilterPanel();

            // إنشاء جدول التقرير
            CreateReportGridView();

            // إنشاء لوحة الملخص
            CreateSummaryPanel();

            this.ResumeLayout(false);
        }

        private void CreateFilterPanel()
        {
            filterPanel = new Panel();
            filterPanel.Height = 120;
            filterPanel.Dock = DockStyle.Top;
            filterPanel.BackColor = Color.LightBlue;

            // عنوان التقرير
            titleLabel = new Label();
            titleLabel.Text = "التقرير الكامل - شركة الغشمي";
            titleLabel.Location = new Point(500, 10);
            titleLabel.Size = new Size(400, 30);
            titleLabel.Font = new Font("Tahoma", 16F, FontStyle.Bold);
            titleLabel.TextAlign = ContentAlignment.MiddleCenter;
            titleLabel.ForeColor = Color.DarkBlue;

            // تسمية تاريخ البداية
            fromDateLabel = new Label();
            fromDateLabel.Text = "من تاريخ:";
            fromDateLabel.Location = new Point(1200, 55);
            fromDateLabel.Size = new Size(80, 23);
            fromDateLabel.TextAlign = ContentAlignment.MiddleRight;

            // منتقي تاريخ البداية
            fromDatePicker = new DateTimePicker();
            fromDatePicker.Location = new Point(1050, 55);
            fromDatePicker.Size = new Size(140, 23);
            fromDatePicker.Format = DateTimePickerFormat.Short;
            fromDatePicker.Value = DateTime.Now.AddDays(-7);

            // تسمية تاريخ النهاية
            toDateLabel = new Label();
            toDateLabel.Text = "إلى تاريخ:";
            toDateLabel.Location = new Point(950, 55);
            toDateLabel.Size = new Size(80, 23);
            toDateLabel.TextAlign = ContentAlignment.MiddleRight;

            // منتقي تاريخ النهاية
            toDatePicker = new DateTimePicker();
            toDatePicker.Location = new Point(800, 55);
            toDatePicker.Size = new Size(140, 23);
            toDatePicker.Format = DateTimePickerFormat.Short;
            toDatePicker.Value = DateTime.Now;

            // زر إنشاء التقرير
            generateButton = new Button();
            generateButton.Text = "إنشاء التقرير";
            generateButton.Size = new Size(120, 35);
            generateButton.Location = new Point(650, 50);
            generateButton.BackColor = Color.LightGreen;
            generateButton.Font = new Font("Tahoma", 10F, FontStyle.Bold);
            generateButton.Click += GenerateButton_Click;

            // زر التصدير
            exportButton = new Button();
            exportButton.Text = "تصدير CSV";
            exportButton.Size = new Size(100, 35);
            exportButton.Location = new Point(540, 50);
            exportButton.BackColor = Color.LightYellow;
            exportButton.Enabled = false;
            exportButton.Click += ExportButton_Click;

            // زر الطباعة
            printButton = new Button();
            printButton.Text = "طباعة";
            printButton.Size = new Size(100, 35);
            printButton.Location = new Point(430, 50);
            printButton.BackColor = Color.LightCyan;
            printButton.Enabled = false;
            printButton.Click += PrintButton_Click;

            // تسمية الفترة
            periodLabel = new Label();
            periodLabel.Text = "اختر الفترة وانقر على 'إنشاء التقرير'";
            periodLabel.Location = new Point(50, 85);
            periodLabel.Size = new Size(400, 25);
            periodLabel.Font = new Font("Tahoma", 10F, FontStyle.Italic);
            periodLabel.ForeColor = Color.DarkGreen;

            filterPanel.Controls.AddRange(new Control[] {
                titleLabel, fromDateLabel, fromDatePicker, toDateLabel, toDatePicker,
                generateButton, exportButton, printButton, periodLabel
            });

            this.Controls.Add(filterPanel);
        }

        private void CreateReportGridView()
        {
            reportGridView = new DataGridView();
            reportGridView.Dock = DockStyle.Fill;
            reportGridView.AutoGenerateColumns = false;
            reportGridView.ReadOnly = true;
            reportGridView.AllowUserToAddRows = false;
            reportGridView.AllowUserToDeleteRows = false;
            reportGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            reportGridView.BackgroundColor = Color.White;
            reportGridView.Font = new Font("Tahoma", 10F);

            // إعداد الأعمدة
            SetupReportColumns();

            this.Controls.Add(reportGridView);
        }

        private void SetupReportColumns()
        {
            var nameColumn = new DataGridViewTextBoxColumn();
            nameColumn.Name = "RaayahName";
            nameColumn.HeaderText = "اسم الرعوي";
            nameColumn.DataPropertyName = "Raayah.FullName";
            nameColumn.Width = 250;

            var samirColumn = new DataGridViewTextBoxColumn();
            samirColumn.Name = "SamirAmount";
            samirColumn.HeaderText = "سمير";
            samirColumn.DataPropertyName = "SamirAmount";
            samirColumn.Width = 120;
            samirColumn.DefaultCellStyle.Format = "N2";
            samirColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            var maherColumn = new DataGridViewTextBoxColumn();
            maherColumn.Name = "MaherAmount";
            maherColumn.HeaderText = "ماهر";
            maherColumn.DataPropertyName = "MaherAmount";
            maherColumn.Width = 120;
            maherColumn.DefaultCellStyle.Format = "N2";
            maherColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            var raidColumn = new DataGridViewTextBoxColumn();
            raidColumn.Name = "RaidAmount";
            raidColumn.HeaderText = "رايد";
            raidColumn.DataPropertyName = "RaidAmount";
            raidColumn.Width = 120;
            raidColumn.DefaultCellStyle.Format = "N2";
            raidColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            var haiderColumn = new DataGridViewTextBoxColumn();
            haiderColumn.Name = "HaiderAmount";
            haiderColumn.HeaderText = "حيدر";
            haiderColumn.DataPropertyName = "HaiderAmount";
            haiderColumn.Width = 120;
            haiderColumn.DefaultCellStyle.Format = "N2";
            haiderColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            var lateColumn = new DataGridViewTextBoxColumn();
            lateColumn.Name = "LateAmount";
            lateColumn.HeaderText = "متأخر";
            lateColumn.DataPropertyName = "LateAmount";
            lateColumn.Width = 120;
            lateColumn.DefaultCellStyle.Format = "N2";
            lateColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            var totalColumn = new DataGridViewTextBoxColumn();
            totalColumn.Name = "TotalDebts";
            totalColumn.HeaderText = "إجمالي الديون";
            totalColumn.DataPropertyName = "TotalDebtsAmount";
            totalColumn.Width = 150;
            totalColumn.DefaultCellStyle.Format = "N2";
            totalColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            totalColumn.DefaultCellStyle.BackColor = Color.LightYellow;
            totalColumn.DefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            var receivedColumn = new DataGridViewTextBoxColumn();
            receivedColumn.Name = "ReceivedAmount";
            receivedColumn.HeaderText = "الواصل";
            receivedColumn.DataPropertyName = "ReceivedAmount";
            receivedColumn.Width = 120;
            receivedColumn.DefaultCellStyle.Format = "N2";
            receivedColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;

            var netColumn = new DataGridViewTextBoxColumn();
            netColumn.Name = "NetAmount";
            netColumn.HeaderText = "الصافي";
            netColumn.Width = 150;
            netColumn.DefaultCellStyle.Format = "N2";
            netColumn.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleRight;
            netColumn.DefaultCellStyle.BackColor = Color.LightGreen;
            netColumn.DefaultCellStyle.Font = new Font("Tahoma", 10F, FontStyle.Bold);

            reportGridView!.Columns.AddRange(new DataGridViewColumn[] {
                nameColumn, samirColumn, maherColumn, raidColumn, haiderColumn,
                lateColumn, totalColumn, receivedColumn, netColumn
            });

            // تنسيق الرؤوس
            reportGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.Navy;
            reportGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            reportGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Tahoma", 11F, FontStyle.Bold);
            reportGridView.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            reportGridView.ColumnHeadersHeight = 40;

            // تنسيق الصفوف
            reportGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.AliceBlue;
            reportGridView.RowsDefaultCellStyle.BackColor = Color.White;
            reportGridView.RowTemplate.Height = 35;

            // حدث تنسيق الخلايا
            reportGridView.CellFormatting += ReportGridView_CellFormatting;
        }

        private void CreateSummaryPanel()
        {
            summaryPanel = new Panel();
            summaryPanel.Height = 100;
            summaryPanel.Dock = DockStyle.Bottom;
            summaryPanel.BackColor = Color.LightGray;

            // تسمية الملخص
            summaryLabel = new Label();
            summaryLabel.Text = "الملخص: لم يتم إنشاء التقرير بعد";
            summaryLabel.Location = new Point(20, 20);
            summaryLabel.Size = new Size(1200, 60);
            summaryLabel.Font = new Font("Tahoma", 12F, FontStyle.Bold);
            summaryLabel.ForeColor = Color.DarkBlue;

            summaryPanel.Controls.Add(summaryLabel);
            this.Controls.Add(summaryPanel);
        }

        private async void GenerateButton_Click(object? sender, EventArgs e)
        {
            try
            {
                generateButton!.Enabled = false;
                generateButton.Text = "جاري الإنشاء...";

                var fromDate = fromDatePicker!.Value.Date;
                var toDate = toDatePicker!.Value.Date;

                if (fromDate > toDate)
                {
                    MessageBox.Show("تاريخ البداية يجب أن يكون قبل تاريخ النهاية", 
                        "خطأ في التاريخ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // تحميل البيانات
                var weeklyData = await _weeklyDebtsRepository.GetByPeriodWithRaayahAsync(fromDate, toDate);
                var reportData = weeklyData.OrderBy(d => d.Raayah.FullName).ToList();

                if (!reportData.Any())
                {
                    MessageBox.Show("لا توجد بيانات للفترة المحددة", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // عرض البيانات
                reportGridView!.DataSource = reportData;

                // تحديث الملخص
                UpdateSummary(reportData, fromDate, toDate);

                // تحديث تسمية الفترة
                periodLabel!.Text = $"فترة التقرير: من {fromDate:dd/MM/yyyy} إلى {toDate:dd/MM/yyyy}";

                // تفعيل الأزرار
                exportButton!.Enabled = true;
                printButton!.Enabled = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء التقرير:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                generateButton!.Enabled = true;
                generateButton.Text = "إنشاء التقرير";
            }
        }

        private void ReportGridView_CellFormatting(object? sender, DataGridViewCellFormattingEventArgs e)
        {
            if (e.RowIndex >= 0 && reportGridView!.Columns[e.ColumnIndex].Name == "NetAmount")
            {
                var debt = reportGridView.Rows[e.RowIndex].DataBoundItem as WeeklyDebts;
                if (debt != null)
                {
                    var netAmount = debt.TotalDebtsAmount - debt.ReceivedAmount;
                    e.Value = netAmount;
                    e.FormattingApplied = true;

                    // تلوين الصافي حسب القيمة
                    if (netAmount > 0)
                        e.CellStyle.ForeColor = Color.DarkGreen;
                    else if (netAmount < 0)
                        e.CellStyle.ForeColor = Color.DarkRed;
                }
            }
        }

        private void UpdateSummary(List<WeeklyDebts> data, DateTime fromDate, DateTime toDate)
        {
            var branches = CalculationService.CalculateBranchesTotals(data);
            var totalDebts = data.Sum(d => d.TotalDebtsAmount);
            var totalReceived = data.Sum(d => d.ReceivedAmount);
            var totalNet = totalDebts - totalReceived;

            summaryLabel!.Text = $"ملخص التقرير الكامل ({fromDate:dd/MM/yyyy} - {toDate:dd/MM/yyyy})\n" +
                                $"سمير: {branches.Samir:N2} | ماهر: {branches.Maher:N2} | رايد: {branches.Raid:N2} | حيدر: {branches.Haider:N2} | متأخر: {branches.Late:N2}\n" +
                                $"إجمالي الديون: {totalDebts:N2} | إجمالي الواصل: {totalReceived:N2} | الصافي: {totalNet:N2} | عدد الرعية: {data.Count}";
        }

        private void ExportButton_Click(object? sender, EventArgs e)
        {
            try
            {
                var data = reportGridView!.DataSource as List<WeeklyDebts>;
                if (data == null || !data.Any())
                {
                    MessageBox.Show("لا توجد بيانات للتصدير", "تنبيه", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                var fromDate = fromDatePicker!.Value.Date;
                var toDate = toDatePicker!.Value.Date;

                var filePath = ExportService.ExportCompleteReportToCsv(data, fromDate, toDate);

                var result = MessageBox.Show($"تم تصدير التقرير بنجاح إلى:\n{filePath}\n\nهل تريد فتح الملف؟", 
                    "تم التصدير", MessageBoxButtons.YesNo, MessageBoxIcon.Information);

                if (result == DialogResult.Yes)
                {
                    System.Diagnostics.Process.Start("notepad.exe", filePath);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في تصدير التقرير:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintButton_Click(object? sender, EventArgs e)
        {
            try
            {
                // إنشاء نافذة معاينة الطباعة
                var printPreview = new PrintPreviewForm();
                printPreview.Text = "معاينة طباعة التقرير الكامل";
                printPreview.WindowState = FormWindowState.Maximized;
                printPreview.RightToLeft = RightToLeft.Yes;
                printPreview.RightToLeftLayout = true;

                // إعداد وثيقة الطباعة
                var printDocument = new System.Drawing.Printing.PrintDocument();
                printDocument.PrintPage += PrintDocument_PrintPage;
                
                printPreview.Document = printDocument;
                printPreview.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"فشل في معاينة الطباعة:\n{ex.Message}", 
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void PrintDocument_PrintPage(object? sender, System.Drawing.Printing.PrintPageEventArgs e)
        {
            // تنفيذ طباعة التقرير
            var graphics = e.Graphics!;
            var font = new Font("Tahoma", 12);
            var headerFont = new Font("Tahoma", 16, FontStyle.Bold);
            var brush = Brushes.Black;
            
            var y = 100;
            var lineHeight = 25;

            // عنوان التقرير
            graphics.DrawString("التقرير الكامل - شركة الغشمي", headerFont, brush, 300, y);
            y += lineHeight * 2;

            // فترة التقرير
            var fromDate = fromDatePicker!.Value.Date;
            var toDate = toDatePicker!.Value.Date;
            graphics.DrawString($"الفترة: من {fromDate:dd/MM/yyyy} إلى {toDate:dd/MM/yyyy}", font, brush, 300, y);
            y += lineHeight * 2;

            // رؤوس الأعمدة
            graphics.DrawString("الرعوي", font, brush, 50, y);
            graphics.DrawString("سمير", font, brush, 200, y);
            graphics.DrawString("ماهر", font, brush, 280, y);
            graphics.DrawString("رايد", font, brush, 360, y);
            graphics.DrawString("حيدر", font, brush, 440, y);
            graphics.DrawString("متأخر", font, brush, 520, y);
            graphics.DrawString("الإجمالي", font, brush, 600, y);
            graphics.DrawString("الواصل", font, brush, 700, y);
            graphics.DrawString("الصافي", font, brush, 800, y);
            
            y += lineHeight;

            // خط فاصل
            graphics.DrawLine(Pens.Black, 50, y, 850, y);
            y += 10;

            // البيانات
            var data = reportGridView!.DataSource as List<WeeklyDebts>;
            if (data != null)
            {
                foreach (var debt in data.Take(25)) // طباعة أول 25 سجل
                {
                    var netAmount = debt.TotalDebtsAmount - debt.ReceivedAmount;
                    
                    graphics.DrawString(debt.Raayah.FullName, font, brush, 50, y);
                    graphics.DrawString(debt.SamirAmount.ToString("N0"), font, brush, 200, y);
                    graphics.DrawString(debt.MaherAmount.ToString("N0"), font, brush, 280, y);
                    graphics.DrawString(debt.RaidAmount.ToString("N0"), font, brush, 360, y);
                    graphics.DrawString(debt.HaiderAmount.ToString("N0"), font, brush, 440, y);
                    graphics.DrawString(debt.LateAmount.ToString("N0"), font, brush, 520, y);
                    graphics.DrawString(debt.TotalDebtsAmount.ToString("N0"), font, brush, 600, y);
                    graphics.DrawString(debt.ReceivedAmount.ToString("N0"), font, brush, 700, y);
                    graphics.DrawString(netAmount.ToString("N0"), font, brush, 800, y);
                    
                    y += lineHeight;
                }
            }

            // تاريخ الطباعة
            graphics.DrawString($"تاريخ الطباعة: {DateTime.Now:dd/MM/yyyy HH:mm}", 
                new Font("Tahoma", 10), brush, 50, e.PageBounds.Height - 100);
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            
            // تعيين التركيز على زر الإنشاء
            generateButton?.Focus();
        }
    }
}
