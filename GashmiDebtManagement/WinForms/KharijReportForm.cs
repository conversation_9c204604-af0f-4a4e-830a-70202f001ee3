using GashmiDebtManagement.Repositories;

namespace GashmiDebtManagement.WinForms
{
    public partial class KharijReportForm : Form
    {
        private readonly IRaayahRepository _raayahRepository;
        private readonly IWeeklyDebtsRepository _weeklyDebtsRepository;

        public KharijReportForm(IRaayahRepository raayahRepository, IWeeklyDebtsRepository weeklyDebtsRepository)
        {
            _raayahRepository = raayahRepository;
            _weeklyDebtsRepository = weeklyDebtsRepository;
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.Text = "خارج الكشف";
            this.Size = new Size(800, 600);
            this.RightToLeft = RightToLeft.Yes;
            this.Font = new Font("Tahoma", 10F);

            var label = new Label();
            label.Text = "خارج الكشف - قيد التطوير";
            label.Dock = DockStyle.Fill;
            label.TextAlign = ContentAlignment.MiddleCenter;
            label.Font = new Font("Tahoma", 16F);

            this.Controls.Add(label);
        }
    }
}
