@echo off
chcp 65001 > nul
title تثبيت نظام إدارة ديون الرعية - شركة الغشمي

echo ========================================
echo    تثبيت نظام إدارة ديون الرعية
echo    شركة الغشمي
echo ========================================
echo.

echo مرحباً بك في معالج تثبيت نظام إدارة ديون الرعية
echo.
echo هذا المعالج سيقوم بـ:
echo ✅ التحقق من متطلبات النظام
echo ✅ إنشاء المجلدات المطلوبة
echo ✅ إعداد قاعدة البيانات
echo ✅ بناء التطبيق
echo ✅ تشغيل الاختبارات
echo.

pause

echo.
echo ========================================
echo    الخطوة 1: التحقق من المتطلبات
echo ========================================
echo.

REM التحقق من .NET
echo جاري التحقق من .NET...
dotnet --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ❌ .NET غير مثبت!
    echo.
    echo يرجى تثبيت .NET 8.0 أو أحدث من:
    echo https://dotnet.microsoft.com/download
    echo.
    echo بعد التثبيت، أعد تشغيل هذا الملف
    pause
    exit /b 1
) else (
    echo ✅ .NET متوفر
    dotnet --version
)

echo.
echo ========================================
echo    الخطوة 2: إنشاء المجلدات
echo ========================================
echo.

call setup.bat
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إنشاء المجلدات
    pause
    exit /b 1
)

echo.
echo ========================================
echo    الخطوة 3: بناء المشروع
echo ========================================
echo.

echo جاري بناء المشروع...
dotnet build --configuration Release
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء المشروع
    echo.
    echo تحقق من:
    echo - تثبيت .NET SDK بدلاً من Runtime فقط
    echo - اتصال الإنترنت لتحميل المكتبات
    echo - صلاحيات الكتابة في المجلد
    pause
    exit /b 1
) else (
    echo ✅ تم بناء المشروع بنجاح
)

echo.
echo ========================================
echo    الخطوة 4: إعداد قاعدة البيانات
echo ========================================
echo.

call setup-db.bat
if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في إعداد قاعدة البيانات
    pause
    exit /b 1
)

echo.
echo ========================================
echo    الخطوة 5: تشغيل الاختبارات
echo ========================================
echo.

echo جاري تشغيل الاختبارات...
dotnet test --configuration Release --no-build --verbosity minimal
if %ERRORLEVEL% NEQ 0 (
    echo ⚠️ فشل في بعض الاختبارات
    echo يمكنك المتابعة ولكن قد تواجه مشاكل
    echo.
    set /p continue=هل تريد المتابعة؟ (y/n): 
    if /i "!continue!" NEQ "y" (
        echo تم إلغاء التثبيت
        pause
        exit /b 1
    )
) else (
    echo ✅ اجتازت جميع الاختبارات
)

echo.
echo ========================================
echo    الخطوة 6: إنشاء اختصارات
echo ========================================
echo.

REM إنشاء اختصار على سطح المكتب (اختياري)
set /p createShortcut=هل تريد إنشاء اختصار على سطح المكتب؟ (y/n): 
if /i "%createShortcut%" EQU "y" (
    echo جاري إنشاء الاختصار...
    
    REM إنشاء ملف VBS لإنشاء الاختصار
    echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
    echo sLinkFile = "%USERPROFILE%\Desktop\نظام إدارة ديون الرعية.lnk" >> CreateShortcut.vbs
    echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
    echo oLink.TargetPath = "%CD%\run.bat" >> CreateShortcut.vbs
    echo oLink.WorkingDirectory = "%CD%" >> CreateShortcut.vbs
    echo oLink.Description = "نظام إدارة ديون الرعية - شركة الغشمي" >> CreateShortcut.vbs
    echo oLink.Save >> CreateShortcut.vbs
    
    cscript CreateShortcut.vbs >nul 2>&1
    del CreateShortcut.vbs >nul 2>&1
    
    if exist "%USERPROFILE%\Desktop\نظام إدارة ديون الرعية.lnk" (
        echo ✅ تم إنشاء الاختصار على سطح المكتب
    ) else (
        echo ⚠️ فشل في إنشاء الاختصار
    )
)

echo.
echo ========================================
echo    الخطوة 7: اختبار التشغيل
echo ========================================
echo.

set /p testRun=هل تريد اختبار تشغيل التطبيق الآن؟ (y/n): 
if /i "%testRun%" EQU "y" (
    echo جاري تشغيل التطبيق للاختبار...
    echo سيتم إغلاق التطبيق تلقائياً بعد 10 ثوانٍ...
    
    start /min dotnet run --configuration Release
    timeout /t 10 /nobreak >nul
    taskkill /f /im dotnet.exe >nul 2>&1
    
    echo ✅ تم اختبار التشغيل
)

echo.
echo ========================================
echo    تم التثبيت بنجاح! 🎉
echo ========================================
echo.
echo 📋 ملخص التثبيت:
echo ├─ ✅ تم التحقق من المتطلبات
echo ├─ ✅ تم إنشاء المجلدات
echo ├─ ✅ تم بناء المشروع
echo ├─ ✅ تم إعداد قاعدة البيانات
echo ├─ ✅ تم تشغيل الاختبارات
echo └─ ✅ النظام جاهز للاستخدام
echo.
echo 🚀 لتشغيل التطبيق:
echo    انقر نقراً مزدوجاً على run.bat
echo    أو استخدم الاختصار على سطح المكتب
echo.
echo 📚 للمساعدة:
echo    ├─ دليل المستخدم: USER_GUIDE.md
echo    ├─ ملف README: README.md
echo    └─ معلومات النظام: info.bat
echo.
echo 🛠️ أدوات إضافية:
echo    ├─ test.bat     - تشغيل الاختبارات
echo    ├─ build.bat    - بناء المشروع
echo    ├─ clean.bat    - تنظيف المشروع
echo    └─ setup-db.bat - إعادة إعداد قاعدة البيانات
echo.

echo تم تطوير هذا النظام بواسطة Augment Agent
echo جميع الحقوق محفوظة © 2024 شركة الغشمي
echo.

pause
