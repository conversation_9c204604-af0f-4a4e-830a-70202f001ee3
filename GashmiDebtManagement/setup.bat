@echo off
chcp 65001 > nul
title إعداد نظام إدارة ديون الرعية

echo ========================================
echo    إعداد نظام إدارة ديون الرعية
echo    شركة الغشمي
echo ========================================
echo.

echo جاري إنشاء المجلدات المطلوبة...
echo.

REM إنشاء مجلد البيانات
if not exist "Data" (
    mkdir Data
    echo ✅ تم إنشاء مجلد Data
) else (
    echo ℹ️ مجلد Data موجود مسبقاً
)

REM إنشاء مجلد النسخ الاحتياطية
if not exist "Backups" (
    mkdir Backups
    echo ✅ تم إنشاء مجلد Backups
) else (
    echo ℹ️ مجلد Backups موجود مسبقاً
)

REM إنشاء مجلد التصدير
if not exist "Exports" (
    mkdir Exports
    echo ✅ تم إنشاء مجلد Exports
) else (
    echo ℹ️ مجلد Exports موجود مسبقاً
)

REM إنشاء مجلد السجلات
if not exist "Logs" (
    mkdir Logs
    echo ✅ تم إنشاء مجلد Logs
) else (
    echo ℹ️ مجلد Logs موجود مسبقاً
)

echo.
echo جاري التحقق من متطلبات النظام...
echo.

REM التحقق من .NET
dotnet --version >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ .NET متوفر
    dotnet --version
) else (
    echo ❌ .NET غير متوفر!
    echo يرجى تثبيت .NET 8.0 أو أحدث من:
    echo https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo.
echo جاري استعادة المكتبات...
dotnet restore
if %ERRORLEVEL% EQU 0 (
    echo ✅ تم استعادة المكتبات بنجاح
) else (
    echo ❌ فشل في استعادة المكتبات
    pause
    exit /b 1
)

echo.
echo ========================================
echo    تم إعداد النظام بنجاح!
echo ========================================
echo.
echo المجلدات المتاحة:
echo 📁 Data     - قاعدة البيانات
echo 📁 Backups  - النسخ الاحتياطية
echo 📁 Exports  - الملفات المصدرة
echo 📁 Logs     - ملفات السجلات
echo.
echo لتشغيل التطبيق:
echo 🚀 انقر نقراً مزدوجاً على run.bat
echo أو استخدم الأمر: dotnet run
echo.
echo لتشغيل الاختبارات:
echo 🧪 انقر نقراً مزدوجاً على test.bat
echo أو استخدم الأمر: dotnet test
echo.

pause
