using GashmiDebtManagement.Models;

namespace GashmiDebtManagement.Services
{
    /// <summary>
    /// خدمة الحسابات والعمليات الحسابية
    /// </summary>
    public class CalculationService
    {
        /// <summary>
        /// معدل خصم الحوالة (3%)
        /// </summary>
        public const decimal DISCOUNT_RATE = 0.03m;

        /// <summary>
        /// حساب إجمالي ديون الفروع
        /// </summary>
        public static decimal CalculateTotalBranchesAmount(WeeklyDebts debt)
        {
            return debt.SamirAmount + debt.MaherAmount + debt.RaidAmount + debt.HaiderAmount;
        }

        /// <summary>
        /// حساب إجمالي الديون (الفروع + المتأخر)
        /// </summary>
        public static decimal CalculateTotalDebtsAmount(WeeklyDebts debt)
        {
            return CalculateTotalBranchesAmount(debt) + debt.LateAmount;
        }

        /// <summary>
        /// حساب خصم الحوالة
        /// </summary>
        public static decimal CalculateDiscountAmount(WeeklyDebts debt, bool enableDiscount = true)
        {
            if (!enableDiscount) return 0;

            var totalDebts = CalculateTotalDebtsAmount(debt);
            return (totalDebts - debt.ReceivedAmount) * DISCOUNT_RATE;
        }

        /// <summary>
        /// حساب الصافي
        /// </summary>
        public static decimal CalculateNetAmount(WeeklyDebts debt, bool enableDiscount = true)
        {
            var totalDebts = CalculateTotalDebtsAmount(debt);
            var discount = CalculateDiscountAmount(debt, enableDiscount);
            return totalDebts - debt.ReceivedAmount - discount;
        }

        /// <summary>
        /// حساب إجماليات فترة معينة
        /// </summary>
        public static (decimal TotalDebts, decimal TotalReceived, decimal TotalDiscount, decimal TotalNet) 
            CalculatePeriodTotals(IEnumerable<WeeklyDebts> debts)
        {
            var totalDebts = debts.Sum(d => CalculateTotalDebtsAmount(d));
            var totalReceived = debts.Sum(d => d.ReceivedAmount);
            var totalDiscount = debts.Sum(d => CalculateDiscountAmount(d, d.Raayah?.EnableDiscount ?? true));
            var totalNet = totalDebts - totalReceived - totalDiscount;

            return (totalDebts, totalReceived, totalDiscount, totalNet);
        }

        /// <summary>
        /// حساب إجماليات الفروع لفترة معينة
        /// </summary>
        public static (decimal Samir, decimal Maher, decimal Raid, decimal Haider, decimal Late) 
            CalculateBranchesTotals(IEnumerable<WeeklyDebts> debts)
        {
            var samir = debts.Sum(d => d.SamirAmount);
            var maher = debts.Sum(d => d.MaherAmount);
            var raid = debts.Sum(d => d.RaidAmount);
            var haider = debts.Sum(d => d.HaiderAmount);
            var late = debts.Sum(d => d.LateAmount);

            return (samir, maher, raid, haider, late);
        }

        /// <summary>
        /// التحقق من صحة فترة الأسبوع (7 أيام)
        /// </summary>
        public static bool IsValidWeekPeriod(DateTime fromDate, DateTime toDate)
        {
            return (toDate - fromDate).Days == 6;
        }

        /// <summary>
        /// الحصول على بداية ونهاية الأسبوع من تاريخ معين
        /// </summary>
        public static (DateTime StartDate, DateTime EndDate) GetWeekPeriod(DateTime date)
        {
            // البحث عن بداية الأسبوع (الأحد)
            var daysFromSunday = (int)date.DayOfWeek;
            var startDate = date.AddDays(-daysFromSunday).Date;
            var endDate = startDate.AddDays(6).Date;

            return (startDate, endDate);
        }

        /// <summary>
        /// تنسيق المبلغ كعملة
        /// </summary>
        public static string FormatCurrency(decimal amount, string currencySymbol = "ريال")
        {
            return $"{amount:N2} {currencySymbol}";
        }

        /// <summary>
        /// تنسيق النسبة المئوية
        /// </summary>
        public static string FormatPercentage(decimal percentage)
        {
            return $"{percentage:P2}";
        }

        /// <summary>
        /// حساب نسبة مبلغ من إجمالي
        /// </summary>
        public static decimal CalculatePercentage(decimal amount, decimal total)
        {
            if (total == 0) return 0;
            return (amount / total) * 100;
        }

        /// <summary>
        /// تقريب المبلغ لأقرب رقمين عشريين
        /// </summary>
        public static decimal RoundAmount(decimal amount)
        {
            return Math.Round(amount, 2, MidpointRounding.AwayFromZero);
        }
    }
}
