using System.IO.Compression;

namespace GashmiDebtManagement.Services
{
    /// <summary>
    /// خدمة النسخ الاحتياطية
    /// </summary>
    public static class BackupService
    {
        private static readonly string BackupDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Backups");

        /// <summary>
        /// إنشاء نسخة احتياطية من قاعدة البيانات
        /// </summary>
        public static string CreateBackup()
        {
            try
            {
                // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
                if (!Directory.Exists(BackupDirectory))
                {
                    Directory.CreateDirectory(BackupDirectory);
                }

                var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GashmiDebtManagement.db");
                if (!File.Exists(dbPath))
                {
                    throw new FileNotFoundException("ملف قاعدة البيانات غير موجود");
                }

                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"GashmiDebtManagement_Backup_{timestamp}.db";
                var backupPath = Path.Combine(BackupDirectory, backupFileName);

                // نسخ ملف قاعدة البيانات
                File.Copy(dbPath, backupPath);

                // ضغط النسخة الاحتياطية (اختياري)
                var zipPath = Path.Combine(BackupDirectory, $"GashmiDebtManagement_Backup_{timestamp}.zip");
                using (var archive = ZipFile.Create(zipPath))
                {
                    archive.Add(backupPath, backupFileName);
                }

                // حذف الملف غير المضغوط
                File.Delete(backupPath);

                return zipPath;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في إنشاء النسخة الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// استعادة نسخة احتياطية
        /// </summary>
        public static void RestoreBackup(string backupPath)
        {
            try
            {
                if (!File.Exists(backupPath))
                {
                    throw new FileNotFoundException("ملف النسخة الاحتياطية غير موجود");
                }

                var dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GashmiDebtManagement.db");
                
                // إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
                if (File.Exists(dbPath))
                {
                    var currentBackup = Path.Combine(BackupDirectory, $"Current_Backup_{DateTime.Now:yyyyMMdd_HHmmss}.db");
                    File.Copy(dbPath, currentBackup);
                }

                // استخراج واستعادة النسخة الاحتياطية
                if (backupPath.EndsWith(".zip"))
                {
                    using (var archive = ZipFile.OpenRead(backupPath))
                    {
                        var entry = archive.Entries.FirstOrDefault(e => e.Name.EndsWith(".db"));
                        if (entry != null)
                        {
                            entry.ExtractToFile(dbPath, true);
                        }
                    }
                }
                else if (backupPath.EndsWith(".db"))
                {
                    File.Copy(backupPath, dbPath, true);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في استعادة النسخة الاحتياطية: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية المتاحة
        /// </summary>
        public static List<BackupInfo> GetAvailableBackups()
        {
            var backups = new List<BackupInfo>();

            if (!Directory.Exists(BackupDirectory))
            {
                return backups;
            }

            var backupFiles = Directory.GetFiles(BackupDirectory, "*.zip")
                .Concat(Directory.GetFiles(BackupDirectory, "*.db"))
                .OrderByDescending(f => File.GetCreationTime(f));

            foreach (var file in backupFiles)
            {
                var fileInfo = new FileInfo(file);
                backups.Add(new BackupInfo
                {
                    FileName = fileInfo.Name,
                    FilePath = file,
                    CreatedDate = fileInfo.CreationTime,
                    Size = fileInfo.Length
                });
            }

            return backups;
        }

        /// <summary>
        /// حذف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 10 نسخ)
        /// </summary>
        public static void CleanOldBackups(int keepCount = 10)
        {
            try
            {
                var backups = GetAvailableBackups();
                var backupsToDelete = backups.Skip(keepCount);

                foreach (var backup in backupsToDelete)
                {
                    File.Delete(backup.FilePath);
                }
            }
            catch (Exception ex)
            {
                // تسجيل الخطأ دون إيقاف التطبيق
                Console.WriteLine($"تحذير: فشل في تنظيف النسخ الاحتياطية القديمة: {ex.Message}");
            }
        }

        /// <summary>
        /// نسخة احتياطية تلقائية عند بدء التطبيق
        /// </summary>
        public static void AutoBackupOnStartup()
        {
            try
            {
                var lastBackup = GetAvailableBackups().FirstOrDefault();
                
                // إنشاء نسخة احتياطية إذا لم تكن هناك نسخة اليوم
                if (lastBackup == null || lastBackup.CreatedDate.Date < DateTime.Today)
                {
                    CreateBackup();
                    CleanOldBackups();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: فشل في النسخة الاحتياطية التلقائية: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// معلومات النسخة الاحتياطية
    /// </summary>
    public class BackupInfo
    {
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public long Size { get; set; }

        public string FormattedSize => FormatFileSize(Size);
        public string FormattedDate => CreatedDate.ToString("dd/MM/yyyy HH:mm");

        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
