using System.Text;
using GashmiDebtManagement.Models;
using GashmiDebtManagement.Helpers;

namespace GashmiDebtManagement.Services
{
    /// <summary>
    /// خدمة تصدير البيانات
    /// </summary>
    public static class ExportService
    {
        private static readonly string ExportsDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Exports");

        /// <summary>
        /// تصدير التقرير الكامل إلى CSV
        /// </summary>
        public static string ExportCompleteReportToCsv(IEnumerable<WeeklyDebts> data, DateTime fromDate, DateTime toDate)
        {
            var csv = new StringBuilder();
            
            // العنوان
            csv.AppendLine($"التقرير الكامل - من {ArabicHelper.FormatArabicDate(fromDate)} إلى {ArabicHelper.FormatArabicDate(toDate)}");
            csv.AppendLine();
            
            // رؤوس الأعمدة
            csv.AppendLine("الرعوي,سمير,ماهر,رايد,حيدر,متأخر,إجمالي الديون,الواصل,الصافي");
            
            // البيانات
            foreach (var debt in data.OrderBy(d => d.Raayah.FullName))
            {
                var netAmount = debt.TotalDebtsAmount - debt.ReceivedAmount;
                csv.AppendLine($"{debt.Raayah.FullName},{debt.SamirAmount},{debt.MaherAmount},{debt.RaidAmount},{debt.HaiderAmount},{debt.LateAmount},{debt.TotalDebtsAmount},{debt.ReceivedAmount},{netAmount}");
            }
            
            // الإجماليات
            var totalDebts = data.Sum(d => d.TotalDebtsAmount);
            var totalReceived = data.Sum(d => d.ReceivedAmount);
            var totalNet = totalDebts - totalReceived;
            
            csv.AppendLine();
            csv.AppendLine($"الإجماليات,,,,,,{totalDebts},{totalReceived},{totalNet}");
            
            return SaveCsvFile(csv.ToString(), $"التقرير_الكامل_{fromDate:yyyyMMdd}_{toDate:yyyyMMdd}");
        }

        /// <summary>
        /// تصدير التقرير مع خصم الحوالة إلى CSV
        /// </summary>
        public static string ExportDiscountReportToCsv(IEnumerable<WeeklyDebts> data, DateTime fromDate, DateTime toDate)
        {
            var csv = new StringBuilder();
            
            // العنوان
            csv.AppendLine($"التقرير مع خصم الحوالة - من {ArabicHelper.FormatArabicDate(fromDate)} إلى {ArabicHelper.FormatArabicDate(toDate)}");
            csv.AppendLine();
            
            // رؤوس الأعمدة
            csv.AppendLine("الرعوي,إجمالي الديون,الواصل,خصم الحوالة,الصافي,حالة الخصم");
            
            // البيانات
            foreach (var debt in data.OrderBy(d => d.Raayah.FullName))
            {
                var discountStatus = debt.Raayah.EnableDiscount ? "مفعل" : "معطل";
                csv.AppendLine($"{debt.Raayah.FullName},{debt.TotalDebtsAmount},{debt.ReceivedAmount},{debt.DiscountAmount},{debt.NetAmount},{discountStatus}");
            }
            
            // الإجماليات
            var totals = CalculationService.CalculatePeriodTotals(data);
            csv.AppendLine();
            csv.AppendLine($"الإجماليات,{totals.TotalDebts},{totals.TotalReceived},{totals.TotalDiscount},{totals.TotalNet},");
            
            return SaveCsvFile(csv.ToString(), $"التقرير_مع_خصم_الحوالة_{fromDate:yyyyMMdd}_{toDate:yyyyMMdd}");
        }

        /// <summary>
        /// تصدير تقرير الفروع إلى CSV
        /// </summary>
        public static string ExportBranchesReportToCsv(IEnumerable<WeeklyDebts> data, DateTime fromDate, DateTime toDate)
        {
            var csv = new StringBuilder();
            
            // العنوان
            csv.AppendLine($"تقرير الفروع - من {ArabicHelper.FormatArabicDate(fromDate)} إلى {ArabicHelper.FormatArabicDate(toDate)}");
            csv.AppendLine();
            
            var branches = CalculationService.CalculateBranchesTotals(data);
            var totalBranches = branches.Samir + branches.Maher + branches.Raid + branches.Haider;
            var grandTotal = totalBranches + branches.Late;
            
            // رؤوس الأعمدة
            csv.AppendLine("الفرع,المبلغ,النسبة من الفروع,النسبة من الإجمالي");
            
            // البيانات
            csv.AppendLine($"سمير,{branches.Samir},{CalculationService.CalculatePercentage(branches.Samir, totalBranches):F2}%,{CalculationService.CalculatePercentage(branches.Samir, grandTotal):F2}%");
            csv.AppendLine($"ماهر,{branches.Maher},{CalculationService.CalculatePercentage(branches.Maher, totalBranches):F2}%,{CalculationService.CalculatePercentage(branches.Maher, grandTotal):F2}%");
            csv.AppendLine($"رايد,{branches.Raid},{CalculationService.CalculatePercentage(branches.Raid, totalBranches):F2}%,{CalculationService.CalculatePercentage(branches.Raid, grandTotal):F2}%");
            csv.AppendLine($"حيدر,{branches.Haider},{CalculationService.CalculatePercentage(branches.Haider, totalBranches):F2}%,{CalculationService.CalculatePercentage(branches.Haider, grandTotal):F2}%");
            csv.AppendLine($"المتأخر,{branches.Late},-,{CalculationService.CalculatePercentage(branches.Late, grandTotal):F2}%");
            
            // الإجماليات
            csv.AppendLine();
            csv.AppendLine($"إجمالي الفروع,{totalBranches},,");
            csv.AppendLine($"الإجمالي العام,{grandTotal},,");
            
            return SaveCsvFile(csv.ToString(), $"تقرير_الفروع_{fromDate:yyyyMMdd}_{toDate:yyyyMMdd}");
        }

        /// <summary>
        /// تصدير التقرير الشهري إلى CSV
        /// </summary>
        public static string ExportMonthlyReportToCsv(IEnumerable<WeeklyDebts> data, DateTime monthDate)
        {
            var csv = new StringBuilder();
            
            // العنوان
            csv.AppendLine($"التقرير الشهري - {ArabicHelper.GetArabicMonthName(monthDate.Month)} {monthDate.Year}");
            csv.AppendLine();
            
            // تجميع البيانات حسب الرعوي
            var groupedData = data
                .GroupBy(d => d.RaayahId)
                .Select(g => new
                {
                    RaayahName = g.First().Raayah.FullName,
                    TotalDebts = g.Sum(d => d.TotalDebtsAmount),
                    TotalReceived = g.Sum(d => d.ReceivedAmount),
                    TotalDiscount = g.Sum(d => d.DiscountAmount),
                    TotalNet = g.Sum(d => d.NetAmount),
                    WeeksCount = g.Count()
                })
                .OrderBy(x => x.RaayahName);
            
            // رؤوس الأعمدة
            csv.AppendLine("الرعوي,عدد الأسابيع,إجمالي الديون,إجمالي الواصل,إجمالي الخصم,الصافي");
            
            // البيانات
            foreach (var item in groupedData)
            {
                csv.AppendLine($"{item.RaayahName},{item.WeeksCount},{item.TotalDebts},{item.TotalReceived},{item.TotalDiscount},{item.TotalNet}");
            }
            
            // الإجماليات الشهرية
            var monthlyTotals = CalculationService.CalculatePeriodTotals(data);
            csv.AppendLine();
            csv.AppendLine($"الإجماليات,,{monthlyTotals.TotalDebts},{monthlyTotals.TotalReceived},{monthlyTotals.TotalDiscount},{monthlyTotals.TotalNet}");
            csv.AppendLine($"عدد الأسابيع,{data.Select(d => d.DateFrom).Distinct().Count()},,,,");
            csv.AppendLine($"عدد الرعية النشطة,{groupedData.Count()},,,,");
            
            return SaveCsvFile(csv.ToString(), $"التقرير_الشهري_{monthDate:yyyyMM}");
        }

        /// <summary>
        /// تصدير قائمة الرعية إلى CSV
        /// </summary>
        public static string ExportRaayahListToCsv(IEnumerable<Raayah> raayahList)
        {
            var csv = new StringBuilder();
            
            // العنوان
            csv.AppendLine($"قائمة الرعية - {ArabicHelper.FormatArabicDate(DateTime.Now)}");
            csv.AppendLine();
            
            // رؤوس الأعمدة
            csv.AppendLine("الرقم,الاسم الكامل,خصم الحوالة,كشف الأوزري,خارج الكشف,تاريخ الإنشاء");
            
            // البيانات
            foreach (var raayah in raayahList.OrderBy(r => r.FullName))
            {
                csv.AppendLine($"{raayah.Id},{raayah.FullName},{(raayah.EnableDiscount ? "نعم" : "لا")},{(raayah.InKashfOzri ? "نعم" : "لا")},{(raayah.InKharijKashf ? "نعم" : "لا")},{ArabicHelper.FormatArabicDate(raayah.CreatedDate)}");
            }
            
            // الإحصائيات
            csv.AppendLine();
            csv.AppendLine($"إجمالي عدد الرعية,{raayahList.Count()},,,,");
            csv.AppendLine($"المفعلين لخصم الحوالة,{raayahList.Count(r => r.EnableDiscount)},,,,");
            csv.AppendLine($"في كشف الأوزري,{raayahList.Count(r => r.InKashfOzri)},,,,");
            csv.AppendLine($"في خارج الكشف,{raayahList.Count(r => r.InKharijKashf)},,,,");
            
            return SaveCsvFile(csv.ToString(), $"قائمة_الرعية_{DateTime.Now:yyyyMMdd}");
        }

        /// <summary>
        /// حفظ ملف CSV
        /// </summary>
        private static string SaveCsvFile(string content, string fileName)
        {
            try
            {
                // إنشاء مجلد التصدير إذا لم يكن موجوداً
                if (!Directory.Exists(ExportsDirectory))
                {
                    Directory.CreateDirectory(ExportsDirectory);
                }

                var filePath = Path.Combine(ExportsDirectory, $"{fileName}.csv");
                
                // كتابة الملف بترميز UTF-8 مع BOM لدعم العربية في Excel
                File.WriteAllText(filePath, content, new UTF8Encoding(true));
                
                return filePath;
            }
            catch (Exception ex)
            {
                throw new Exception($"فشل في حفظ ملف CSV: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على قائمة الملفات المصدرة
        /// </summary>
        public static List<ExportInfo> GetExportedFiles()
        {
            var exports = new List<ExportInfo>();

            if (!Directory.Exists(ExportsDirectory))
            {
                return exports;
            }

            var exportFiles = Directory.GetFiles(ExportsDirectory, "*.csv")
                .OrderByDescending(f => File.GetCreationTime(f));

            foreach (var file in exportFiles)
            {
                var fileInfo = new FileInfo(file);
                exports.Add(new ExportInfo
                {
                    FileName = fileInfo.Name,
                    FilePath = file,
                    CreatedDate = fileInfo.CreationTime,
                    Size = fileInfo.Length
                });
            }

            return exports;
        }

        /// <summary>
        /// حذف الملفات المصدرة القديمة
        /// </summary>
        public static void CleanOldExports(int daysToKeep = 30)
        {
            try
            {
                if (!Directory.Exists(ExportsDirectory))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var oldFiles = Directory.GetFiles(ExportsDirectory, "*.csv")
                    .Where(f => File.GetCreationTime(f) < cutoffDate);

                foreach (var file in oldFiles)
                {
                    File.Delete(file);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"تحذير: فشل في تنظيف الملفات المصدرة القديمة: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// معلومات الملف المصدر
    /// </summary>
    public class ExportInfo
    {
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public DateTime CreatedDate { get; set; }
        public long Size { get; set; }

        public string FormattedSize => FormatFileSize(Size);
        public string FormattedDate => CreatedDate.ToString("dd/MM/yyyy HH:mm");

        private static string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
}
