{"format": 1, "restore": {"/home/<USER>/Desktop/gasmecc#/GashmiDebtManagement/GashmiDebtManagement.csproj": {}}, "projects": {"/home/<USER>/Desktop/gasmecc#/GashmiDebtManagement/GashmiDebtManagement.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Desktop/gasmecc#/GashmiDebtManagement/GashmiDebtManagement.csproj", "projectName": "GashmiDebtManagement", "projectPath": "/home/<USER>/Desktop/gasmecc#/GashmiDebtManagement/GashmiDebtManagement.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Desktop/gasmecc#/GashmiDebtManagement/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net6.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"ConsoleTables": {"target": "Package", "version": "[2.4.2, )"}, "Microsoft.EntityFrameworkCore.Design": {"target": "Package", "version": "[6.0.10, )"}, "Microsoft.EntityFrameworkCore.Sqlite": {"target": "Package", "version": "[6.0.10, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[6.0.10, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.8, 6.0.8]"}, {"name": "Microsoft.NETCore.App.Host.linux-x64", "version": "[6.0.8, 6.0.8]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.8, 6.0.8]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/6.0.400/RuntimeIdentifierGraph.json"}}}}}