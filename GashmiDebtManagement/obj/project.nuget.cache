{"version": 2, "dgSpecHash": "emPlQJ6HoQBgbQREfU4CYYGJgYkijmWWxEue8TRCi1vPPTMz0P1chEZihEVB6KIB3qlZAK4q94f7NiESByBfBw==", "success": true, "projectFilePath": "/home/<USER>/Desktop/gasmecc#/GashmiDebtManagement/GashmiDebtManagement.csproj", "expectedPackageFiles": ["/home/<USER>/.nuget/packages/consoletables/2.4.2/consoletables.2.4.2.nupkg.sha512", "/home/<USER>/.nuget/packages/humanizer.core/2.8.26/humanizer.core.2.8.26.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.data.sqlite.core/6.0.10/microsoft.data.sqlite.core.6.0.10.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore/6.0.10/microsoft.entityframeworkcore.6.0.10.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore.abstractions/6.0.10/microsoft.entityframeworkcore.abstractions.6.0.10.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore.analyzers/6.0.10/microsoft.entityframeworkcore.analyzers.6.0.10.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore.design/6.0.10/microsoft.entityframeworkcore.design.6.0.10.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore.relational/6.0.10/microsoft.entityframeworkcore.relational.6.0.10.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore.sqlite/6.0.10/microsoft.entityframeworkcore.sqlite.6.0.10.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore.sqlite.core/6.0.10/microsoft.entityframeworkcore.sqlite.core.6.0.10.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.entityframeworkcore.tools/6.0.10/microsoft.entityframeworkcore.tools.6.0.10.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.caching.abstractions/6.0.0/microsoft.extensions.caching.abstractions.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.caching.memory/6.0.1/microsoft.extensions.caching.memory.6.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.configuration.abstractions/6.0.0/microsoft.extensions.configuration.abstractions.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection/6.0.1/microsoft.extensions.dependencyinjection.6.0.1.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencyinjection.abstractions/6.0.0/microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.dependencymodel/6.0.0/microsoft.extensions.dependencymodel.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging/6.0.0/microsoft.extensions.logging.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.logging.abstractions/6.0.0/microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.options/6.0.0/microsoft.extensions.options.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.extensions.primitives/6.0.0/microsoft.extensions.primitives.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.netcore.platforms/1.1.0/microsoft.netcore.platforms.1.1.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.netcore.targets/1.1.0/microsoft.netcore.targets.1.1.0.nupkg.sha512", "/home/<USER>/.nuget/packages/sqlitepclraw.bundle_e_sqlite3/2.0.6/sqlitepclraw.bundle_e_sqlite3.2.0.6.nupkg.sha512", "/home/<USER>/.nuget/packages/sqlitepclraw.core/2.0.6/sqlitepclraw.core.2.0.6.nupkg.sha512", "/home/<USER>/.nuget/packages/sqlitepclraw.lib.e_sqlite3/2.0.6/sqlitepclraw.lib.e_sqlite3.2.0.6.nupkg.sha512", "/home/<USER>/.nuget/packages/sqlitepclraw.provider.e_sqlite3/2.0.6/sqlitepclraw.provider.e_sqlite3.2.0.6.nupkg.sha512", "/home/<USER>/.nuget/packages/system.buffers/4.5.1/system.buffers.4.5.1.nupkg.sha512", "/home/<USER>/.nuget/packages/system.collections.immutable/6.0.0/system.collections.immutable.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.diagnostics.diagnosticsource/6.0.0/system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.io/4.3.0/system.io.4.3.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.memory/4.5.4/system.memory.4.5.4.nupkg.sha512", "/home/<USER>/.nuget/packages/system.reflection/4.3.0/system.reflection.4.3.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.reflection.primitives/4.3.0/system.reflection.primitives.4.3.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.reflection.typeextensions/4.3.0/system.reflection.typeextensions.4.3.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.runtime/4.3.0/system.runtime.4.3.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.runtime.compilerservices.unsafe/6.0.0/system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.encoding/4.3.0/system.text.encoding.4.3.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.encodings.web/6.0.0/system.text.encodings.web.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.text.json/6.0.0/system.text.json.6.0.0.nupkg.sha512", "/home/<USER>/.nuget/packages/system.threading.tasks/4.3.0/system.threading.tasks.4.3.0.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.netcore.app.ref/6.0.8/microsoft.netcore.app.ref.6.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.aspnetcore.app.ref/6.0.8/microsoft.aspnetcore.app.ref.6.0.8.nupkg.sha512", "/home/<USER>/.nuget/packages/microsoft.netcore.app.host.linux-x64/6.0.8/microsoft.netcore.app.host.linux-x64.6.0.8.nupkg.sha512"], "logs": []}